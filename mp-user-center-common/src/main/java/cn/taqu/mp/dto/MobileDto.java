package cn.taqu.mp.dto;

import cn.taqu.mp.annotation.SearchIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 手机号模块
 *
 * <AUTHOR>
 * @date 2025/7/3 下午2:51
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MobileDto implements Serializable, MappingKey, Categorized {

    /**
     * 手机号明文【对外接口不建议随意提供明文手机号，该字段仅限于内部使用】
     */
    private String mobile;

    /**
     * 是否绑定手机号
     */
    @SearchIgnore
    private String isBindMobile;

}
