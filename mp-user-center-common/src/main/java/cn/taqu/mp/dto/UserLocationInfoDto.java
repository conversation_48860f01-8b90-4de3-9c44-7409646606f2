package cn.taqu.mp.dto;

import cn.taqu.mp.annotation.Compromise;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/21 下午2:51
 */
@Data
public class UserLocationInfoDto implements Serializable, MappingKey, Categorized {

    @Compromise
    private Double latitude;

    @Compromise
    private Double longitude;

    /**
     * 具体cityId, 具体到对应城市。并非city字段里的省份, 城市。
     * 目前看着没有需要的业务场景，临时字段
     */
    private Long cityId;

}
