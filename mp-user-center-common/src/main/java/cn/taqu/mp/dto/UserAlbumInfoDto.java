package cn.taqu.mp.dto;

import cn.taqu.mp.util.AvatarTool;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 用户相册 + 生活
 * <AUTHOR>
 * @date 2025/1/21 下午2:51
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public record UserAlbumInfoDto(List<? extends PhotoInfo> photoList) implements Serializable, Categorized {

    public static final UserAlbumInfoDto UNKNOWN = new UserAlbumInfoDto(List.of());

    @Data
    @EqualsAndHashCode(callSuper = true)
    @JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy.class)
    public static class LifeInfo extends PhotoInfo {

    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PhotoInfo {
        /**
         * 图片地址（相对）
         */
        private String photoUrl;

        /**
         * 图片宽
         */
        private Integer width;

        /**
         * 图片高
         */
        private Integer height;

        /**
         * 排序字段
         */
        private Integer seq;

        /**
         * 状态
         */
        private Byte status;

        /**
         * 备注
         */
        private String remark;

        public void setPhotoUrl(String photoUrl) {
            this.photoUrl = AvatarTool.resolve(photoUrl);
        }
    }


}
