package cn.taqu.mp.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/28 上午11:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PhotoInfoDto {

    private String accountUuid;
    /**
     * 图片地址（相对）
     */
    private String photoUrl;

    /**
     * 图片宽
     */
    private Integer width;

    /**
     * 图片高
     */
    private Integer height;

    /**
     * 排序字段
     */
    private Byte seq;

    private Byte status;

    private Byte verifyStatus;

    private String remark;

    public PhotoInfoDto(String accountUuid, String photoUrl, Byte seq) {
        this.accountUuid = accountUuid;
        this.photoUrl = photoUrl;
        this.seq = seq;
    }
}
