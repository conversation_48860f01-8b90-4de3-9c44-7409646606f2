package cn.taqu.mp.dto;

import cn.taqu.mp.annotation.SearchIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

import static cn.taqu.mp.constant.UserInfoConstant.*;

/**
 * <AUTHOR>
 * @date 2025/1/21 下午2:51
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserCoreInfoDto implements Serializable, Categorized, MappingKey {

    public static final UserCoreInfoDto UNKNOWN = new UserCoreInfoDto();

    @SearchIgnore
    private String uuid;

    private String accountName = DEFAULT_ACCOUNT_NAME;

    private String sexType = DEFAULT_SEX_TYPE;

    private String avatar = DEFAULT_AVATAR;

    private String avatarOrigin = DEFAULT_AVATAR;

    private Long birth;

    @SearchIgnore
    private String age = DEFAULT_AGE;

    private String loginAppcode;

    private String loginCloned;

    private String createTime;

    private String platformId;

    private String cityId;

    private String lastPlatformId;

    private String lastActiveAppVersion;

    private String appVersion;

    /**
     * 最新登录版本号，后续会和appVersion进行整合
     */
    private String lastAppVersion;

    private String accountStatus = DEFAULT_ACCOUNT_STATUS;

    private String zhimaCertification = DEFAULT_ZHI_MA_CERTIFICATION;

    private String realPersonCertification = DEFAULT_REAL_PERSON_CERTIFICATION;

    private String regStyle;

    private String channel;

    private String accountCardId;

    private String latitude;

    private String longitude;

}
