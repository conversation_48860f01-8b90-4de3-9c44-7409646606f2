package cn.taqu.mp.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/28 下午3:02
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserSchoolInfoDto implements Serializable, Categorized {

    public static final UserSchoolInfoDto UNKNOWN = new UserSchoolInfoDto();

    private Long schoolId;

    private String schoolName = "";

    private String major = "";

}
