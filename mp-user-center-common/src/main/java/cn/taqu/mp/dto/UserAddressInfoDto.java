package cn.taqu.mp.dto;

import cn.taqu.mp.annotation.SearchIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户地址信息DTO
 *
 * <AUTHOR>
 * @date 2025/7/18
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserAddressInfoDto implements Serializable, MappingKey, Categorized {

    public static final UserAddressInfoDto UNKNOWN = new UserAddressInfoDto();

    /**
     * 城市ID
     */
    @SearchIgnore
    private Long cityId;

    /**
     * 省份ID
     */
    @SearchIgnore
    private Long provinceId;

    /**
     * baseaddr字段，格式为"provinceId,cityId"
     */
    private String baseaddr;
}
