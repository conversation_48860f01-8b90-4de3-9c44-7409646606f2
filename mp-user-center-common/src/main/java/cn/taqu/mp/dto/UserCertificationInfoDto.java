package cn.taqu.mp.dto;

import cn.taqu.mp.annotation.SearchIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.common.base.Strings;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

import static cn.taqu.mp.constant.UserInfoConstant.*;

/**
 * <AUTHOR>
 * @date 2025/1/21 下午2:51
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserCertificationInfoDto implements Serializable, MappingKey, Categorized {

    public static final UserCertificationInfoDto UNKNOWN = new UserCertificationInfoDto();

    private String faceCertification = DEFAULT_FACE_CERTIFICATION;

    private String zhimaCertification = DEFAULT_ZHI_MA_CERTIFICATION;

    private String realPersonCertification = DEFAULT_REAL_PERSON_CERTIFICATION;

    private String realPersonCertificationStatus = DEFAULT_REAL_PERSON_CERTIFICATION;

    private String voiceCertification = DEFAULT_VOICE_CERTIFICATION;

    private String genderCertification = "0";

    private String preAvatarFaceStatus = "0";

    private String registerAvatarStatus = "0";

    private String realAvatarCertification = "0";

    private String identityNoBirth;

    @SearchIgnore
    private String identityNoDesc = StringUtils.EMPTY;

    @SearchIgnore
    private String faceCertificationDesc;

}
