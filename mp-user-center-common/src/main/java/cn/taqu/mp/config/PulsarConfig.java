package cn.taqu.mp.config;

import cn.taqu.etcd.annotation.EtcdValue;
import cn.taqu.mp.factory.PulsarFactory;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/9/11 下午5:12
 */
@Configuration
public class PulsarConfig {

    @EtcdValue(value = "storage/pulsar/url", defaultValue = "pulsar://pulsar1.test.hbmonitor.com:6650")
    private String url;

    @Bean
    PulsarClient pulsarClient() throws PulsarClientException {
        return PulsarClient.builder()
            .connectionTimeout(3, TimeUnit.SECONDS)
            .enableTcpNoDelay(true)
            .ioThreads(1)
            .serviceUrl(url)
            .build();
    }

    @Bean
    PulsarFactory pulsarFactory(PulsarClient client) {
        return new PulsarFactory(client);
    }

}
