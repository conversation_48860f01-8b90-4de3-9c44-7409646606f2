package cn.taqu.mp.config;

import cn.taqu.etcd.annotation.EtcdValue;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午2:03
 */
@Configuration
public class RdbConfig {

    @EtcdValue("storage/mysql/url")
    private String url;

    @EtcdValue("storage/mysql/username")
    private String userName;

    @EtcdValue("storage/mysql/password")
    private String password;

    @Bean
    @Primary
    DataSource dataSource(@Value("${datasource.driver-class-name}") String driverClassName,
                          @Value("${datasource.hikari.maximum-pool-size}") int maxPoolSize,
                          @Value("${datasource.hikari.minimum-idle}") int minIdle,
                          @Value("${datasource.hikari.auto-commit}") boolean autoCommit,
                          @Value("${datasource.hikari.idle-timeout}") int idleTimeout,
                          @Value("${datasource.hikari.ds.pool-name}") String poolName,
                          @Value("${datasource.hikari.max-lifetime}") int maxLifetime,
                          @Value("${datasource.hikari.connection-timeout}") int connTimeout,
                          @Value("${datasource.hikari.connection-test-query}") String testSql,
                          @Value("${datasource.hikari.init-sql}") String initSql
    ) {
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(url);
        hikariConfig.setUsername(userName);
        hikariConfig.setPassword(password);
        hikariConfig.setDriverClassName(driverClassName);
        hikariConfig.setMaximumPoolSize(maxPoolSize);
        hikariConfig.setMinimumIdle(minIdle);
        hikariConfig.setAutoCommit(autoCommit);
        hikariConfig.setIdleTimeout(idleTimeout);
        hikariConfig.setPoolName(poolName);
        hikariConfig.setMaxLifetime(maxLifetime);
        hikariConfig.setConnectionTimeout(connTimeout);
        hikariConfig.setConnectionTestQuery(testSql);
        hikariConfig.setConnectionInitSql(initSql);
        return new HikariDataSource(hikariConfig);
    }

    @Bean
    @Primary
    JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("cn.taqu.mp.entity");
        em.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
        em.setEntityManagerFactoryInterface(jakarta.persistence.EntityManagerFactory.class);
        return em;
    }
}
