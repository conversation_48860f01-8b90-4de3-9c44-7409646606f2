package cn.taqu.mp.config;

import cn.taqu.etcd.annotation.EtcdValue;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.Kryo5Codec;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2025/1/15 上午9:42
 */
@Configuration
public class RedisConfig {

    @EtcdValue(value = "storage/redis/default/host")
    private String host;

    @EtcdValue(value = "storage/redis/default/port")
    private Integer port;

    @EtcdValue(value = "storage/redis/biz/host")
    private String bizHost;

    @EtcdValue(value = "storage/redis/biz/port")
    private Integer bizPort;

    @Bean
    @Primary
    StringRedisTemplate redisTemplate() {
        return new StringRedisTemplate(redisConnectionFactory(host, port));
    }

    @Bean
    StringRedisTemplate bizRedisTemplate() {
        return new StringRedisTemplate(redisConnectionFactory(bizHost, bizPort));
    }

    @Bean(destroyMethod = "shutdown")
    RedissonClient redissonClient() {
        return redissonClient(host, port);
    }

    @Bean(destroyMethod = "shutdown")
    RedissonClient redissonBizClient() {
        return redissonClient(bizHost, bizPort);
    }

    RedisConnectionFactory redisConnectionFactory(String host, int port) {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration(host, port);
        GenericObjectPoolConfig<?> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMinIdle(16);
        poolConfig.setMaxIdle(64);
        poolConfig.setMaxWaitMillis(Duration.ofSeconds(3).toMillis());
        poolConfig.setMaxTotal(512);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setMinEvictableIdleTimeMillis(Duration.ofMinutes(1).toMillis());
        poolConfig.setTimeBetweenEvictionRunsMillis(Duration.ofSeconds(30).toMillis());
        poolConfig.setNumTestsPerEvictionRun(-1);
        var factory = new LettuceConnectionFactory(
            redisStandaloneConfiguration,
            LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.ofMillis(2000))
                .poolConfig(poolConfig).build()
        );
        factory.afterPropertiesSet();
        return factory;
    }

    private RedissonClient redissonClient(String host, int port) {
        Config config = new Config();
        config.setUseScriptCache(true);
        config.setCodec(new Kryo5Codec());
        SingleServerConfig singleConfig = config.useSingleServer();
        singleConfig.setAddress("redis://" + host + ":" + port);
        singleConfig.setConnectionPoolSize(512);
        singleConfig.setIdleConnectionTimeout(30000);
        singleConfig.setConnectTimeout(3000);
        singleConfig.setTimeout(2000);
        singleConfig.setRetryInterval(2000);
        singleConfig.setKeepAlive(true);
        return Redisson.create(config);
    }

}
