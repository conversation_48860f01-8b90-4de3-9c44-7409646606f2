package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * 学校信息
 * <AUTHOR>
 * @date 2025/4/28 下午3:24
 */
@Getter
@Setter
@Entity
@Table(name = "school")
public class School extends BaseEntity {

    /**
     *
     */
    @Column(name = "school_name")
    private String schoolName;

    /**
     * 5大专 6大学
     */
    @Column(name = "school_type")
    private Long schoolType;


    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;
}
