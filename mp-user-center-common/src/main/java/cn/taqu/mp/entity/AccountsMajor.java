package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/28 下午3:24
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_major")
public class AccountsMajor extends BaseEntity {

    @Column(name = "account_uuid")
    private String accountUuid;

    @Column(name = "school_id")
    private Long schoolId;

    /**
     * 客态
     */
    private String major;

    /**
     * 主态
     */
    @Column(name = "major_origin")
    private String majorOrigin;


    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;
}
