package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 账号标签配置
 */
@Data
@Entity
@Table(name = "account_label_cfg")
public class AccountLabelCfg extends BaseEntity {

    /**
     * 内容（包含分类、标签），不能为空，默认值为空字符串
     */
    private String content;

    /**
     * 父id，父id=0标识分类，不能为空，默认值为0
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 图标icon，不能为空，默认值为空字符串
     */
    @Column(name = "icon_url")
    private String iconUrl;

    /**
     * 状态，0-禁用，1-启用，不能为空，默认值为0
     */
    @Column(name = "data_status")
    private Integer dataStatus;

    /**
     * 排序，不能为空，默认值为0
     */
    private Integer sort;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 说明
     */
    private String introduce;

}
