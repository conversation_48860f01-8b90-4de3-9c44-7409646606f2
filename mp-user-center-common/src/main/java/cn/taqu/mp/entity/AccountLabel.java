package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

/**
 * 账号标签
 */
@Data
@Entity
@Table(name = "account_label")
public class AccountLabel extends BaseEntity {

    @Column(name = "account_uuid")
    private String accountUuid;

    @Column(name = "label_ids")
    private String labelIds;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;

}
