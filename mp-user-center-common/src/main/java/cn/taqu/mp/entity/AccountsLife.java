package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/4 下午1:35
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_life")
public class AccountsLife extends BaseEntity {

    /**
     * 用户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 图片地址（相对） 主态
     */
    @Column(name = "photo_url_origin")
    private String photoUrlOrigin;

    /**
     * 图片宽 主态
     */
    @Column(name = "width_origin")
    private Integer widthOrigin;

    /**
     * 图片高 主态
     */
    @Column(name = "height_origin")
    private Integer heightOrigin;

    /**
     * 图片描述 主态
     */
    @Column(name = "remark_origin")
    private String remarkOrigin;

    /**
     * 图片地址（相对）
     */
    @Column(name = "photo_url")
    private String photoUrl;

    /**
     * 图片宽
     */
    @Column(name = "width")
    private Integer width;

    /**
     * 图片高
     */
    @Column(name = "height")
    private Integer height;

    /**
     * 图片描述
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 排序字段
     */
    @Column(name = "seq")
    private Byte seq;

    /**
     * 审核状态，枚举 主态
     */
    @Column(name = "status")
    private Byte status;

    /**
     * 对比活体底图认证状态 主态
     */
    @Column(name = "verify_status")
    private Byte verifyStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;



    public enum Status {

        /**
         * 默认 -1
         */
        NONE( -1),
        /**
         * 审核中 0
         */
        AUDITING(0),
        /**
         * 审核通过 1
         */
        AUDIT_SUCCESS(1),
        /**
         * 审核拒绝 2 正常不会有，审核拒绝删除图片
         */
        AUDIT_FAIL(2),
        /**
         * 相册机审通过 3
         */
        PHOTO_AUTO_CHECK(3);

        private final byte value;

        Status(int value) {
            this.value = (byte) value;
        }

        public byte getValue() {
            return value;
        }

        public static Status of(Byte value) {
            if (value == null) {
                return null;
            }
            Status[] enums = Status.values();
            for (Status e : enums) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            return null;
        }

    }

    /**
     * 客态可见状态
     *
     * @return
     */
    public static boolean verifyVisitorStatus(byte status) {
        return status == Status.AUDIT_SUCCESS.getValue()
            || status == Status.PHOTO_AUTO_CHECK.getValue();
    }

}
