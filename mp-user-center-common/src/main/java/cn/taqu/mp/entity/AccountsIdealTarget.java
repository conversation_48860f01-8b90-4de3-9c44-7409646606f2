package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/4/28 下午4:24
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_ideal_target")
public class AccountsIdealTarget extends BaseEntity {

    /**
     *
     */
    @Column(name = "account_uuid")
    private String accountUuid;


    @Column(name = "ideal_target_origin")
    private String idealTargetOrigin;

    @Column(name = "ideal_target")
    private String idealTarget;

    /**
     * 创建时间，不能为空，默认值为0
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间，不能为空，默认值为0
     */
    @Column(name = "update_time")
    private Long updateTime;
}
