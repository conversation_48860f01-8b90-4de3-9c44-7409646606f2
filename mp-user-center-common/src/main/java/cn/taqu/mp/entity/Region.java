package cn.taqu.mp.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * 地理位置表
 *
 * <AUTHOR>
 * 2018年1月3日 下午3:55:11
 */
@Setter
@Getter
@Entity
@Table(name = "region")
public class Region extends BaseEntity {

    private static final long serialVersionUID = 1832397260969265012L;

    private String sign;//标记
    private Long parent_id;//父地区id
    private String region_path;//
    private Integer level;//地区级别
    private String region_name;//地区名称
    private Integer is_unavailable;//是否不使用   0-使用，1-不使用
    private Integer sort;//排序
    private Integer postcode;//邮编
    private String longitude;//经度
    private String latitude;//纬度

}
