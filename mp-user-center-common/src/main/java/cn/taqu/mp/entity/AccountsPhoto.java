package cn.taqu.mp.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2025/3/4 下午1:35
 */
@Getter
@Setter
@Entity
@Table(name = "accounts_photo")
public class AccountsPhoto extends BaseEntity {

    /**
     * 用户uuid
     */
    @Column(name = "account_uuid")
    private String accountUuid;

    /**
     * 图片地址（相对）
     */
    @Column(name = "photo_url")
    private String photoUrl;

    /**
     * 排序字段
     */
    @Column(name = "seq")
    private Byte seq;

    /**
     * 审核状态，枚举 主态
     */
    @Column(name = "status")
    private Byte status;

    /**
     * 对比活体底图认证状态 主态
     */
    @Column(name = "verify_status")
    private Byte verifyStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Long updateTime;

    public enum Status {

        /**
         * -1已处罚
         */
        ILLEGAL(-1),
        /**
         * 1-未处理
         */
        NO_CHECK(1),
        /**
         * 2已忽略
         */
        PASS(2),
        /**
         * 3默认头像
         */
        DEFAULT_PIC(3),
        /**
         * 4待复审(已审核过一次，可以展示了，但是头像抽查时仍然会出现)
         */
        RE_CHECK(4),
        /**
         * 5.等待机审
         */
        AUTO_CHECK(5),
        /**
         * 6.用户操作后的待审核状态，2020.02.05临时处理
         */
        NO_CHECK_ACCOUNT(6),
        /**
         * 7.90天重新活跃，复审
         */
        REVIEW_90(7),
        /**
         * 8.只过机审
         */
        ONLY_AUTO_CHECK(8),
        /**
         * 9.我的生活机审通过
         */
        ACCOUNTS_LIFE_AUTO_CHECK(9);

        private final byte value;

        Status(int value) {
            this.value = (byte) value;
        }

        public byte getValue() {
            return value;
        }

        public static Status of(Integer value) {
            if (value == null) {
                return null;
            }
            Status[] enums = Status.values();
            for (Status e : enums) {
                if (e.getValue() == value) {
                    return e;
                }
            }
            return null;
        }

    }


    /**
     * 客态可见状态
     *
     * @return
     */
    public static boolean verifyVisitorStatus(byte status) {
        return status == Status.PASS.getValue()
            || status == Status.DEFAULT_PIC.getValue()
            || status == Status.RE_CHECK.getValue()
            || status == Status.REVIEW_90.getValue();
    }


}
