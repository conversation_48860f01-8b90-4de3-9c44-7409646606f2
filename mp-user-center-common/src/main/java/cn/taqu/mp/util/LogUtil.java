package cn.taqu.mp.util;

import cn.taqu.core.utils.LocalConfUtil;
import cn.taqu.mp.annotation.DynamicValue;
import cn.taqu.mp.annotation.RefreshScope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * 简单打测试环境日志，因为系统设置测试环境日志级别为info。
 * 防止测试期间日志带上线，造成内存飙升。
 *
 * <AUTHOR>
 * @date 2024/12/12 15:39
 */
@Slf4j
@Component
@RefreshScope
public class LogUtil {

    public static volatile Boolean enabled;

    @DynamicValue("${logging.ctrl.enabled:true}")
    public void setLogging(Boolean b) {
        LogUtil.enabled = b;
    }

    /**
     * 测试环境
     *
     * @param content
     * @param val
     */
    public static void info4Dev(String content, Object... val) {
        if (isTestEnv()) {
            log.info(content, val);
        }
    }

    /**
     * 测试环境
     *
     * @param content
     * @param val
     */
    public static void info4Gray(String content, Object... val) {
        String localEnv = LocalConfUtil.getLocalEnv();
        boolean gray = localEnv.equals("gray");
        if (gray) {
            log.info(content, val);
            return;
        }

        if (localEnv.startsWith("test")) {
            log.info(content, val);
        }
    }

    public static void infoCtl(String content, Object... val) {
        if (BooleanUtils.isTrue(enabled)) {
            log.info(content, val);
        }
    }

    /**
     * 是否测试环境
     *
     * @return
     */
    private static boolean isTestEnv() {
        try {
            String localEnv = LocalConfUtil.getLocalEnv();
            return localEnv.startsWith("test");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 是否灰度环境
     *
     * @return
     */
    private static boolean isGrayEnv() {
        try {
            String localEnv = LocalConfUtil.getLocalEnv();
            return localEnv.equals("gray");
        } catch (Exception e) {
            return false;
        }
    }
}
