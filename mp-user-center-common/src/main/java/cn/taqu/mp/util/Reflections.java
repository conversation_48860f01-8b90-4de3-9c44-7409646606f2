package cn.taqu.mp.util;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2025/4/18 下午1:35
 */
public class Reflections {

    /**
     * 范型接口参数获取
     * @param clz
     * @param interfaceType
     * @param typeIndex
     * @return
     */
    public static <C> Class<?> genericType(Class<C> clz, Class<? super C> interfaceType, int typeIndex) {
        Type[] interfaces = clz.getGenericInterfaces();
        for (Type type : interfaces) {
            if (type instanceof ParameterizedType pt && pt.getRawType().equals(interfaceType)) {
                return (Class<?>) pt.getActualTypeArguments()[typeIndex];
            }
        }
        return null;
    }

}
