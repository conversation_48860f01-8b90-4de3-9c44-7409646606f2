package cn.taqu.mp.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 工具类型service，主要用于其他service中一些数据的通用处理
 *
 * @author:<PERSON><PERSON><PERSON>
 * @date:2017年3月8日 上午10:52:30
 */
@Slf4j
public class AgeTool {

    public static int calAgeFromBirth(long birth) {
        String[] nowDateArray = DateUtil.dateToString10(new Date()).split("-");
        String[] birthArray = DateUtil.dateToString10(DateUtil.fromSecond(birth)).split("-");

        int age = Integer.parseInt(nowDateArray[0]) - Integer.parseInt(birthArray[0]);
        if (age <= 0) {
            return 0;
        }

        int nowMonth = Integer.parseInt(nowDateArray[1]);
        int birthMonth = Integer.parseInt(birthArray[1]);
        if (nowMonth < birthMonth) {
            return age - 1;
        }
        if (nowMonth > birthMonth) {
            return age;
        }

        int nowDay = Integer.parseInt(nowDateArray[2]);
        int birthDay = Integer.parseInt(birthArray[2]);
        if (nowDay < birthDay) {
            return age - 1;
        }
        return age;
    }
}
