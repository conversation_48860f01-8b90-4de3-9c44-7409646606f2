package cn.taqu.mp.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/28 下午3:48
 */
@Slf4j
public class AvatarTool {

    public static String DEFAULT_MALE_AVATAR = "avatar/newavatarmale.jpg";

    public static String DEFAULT_FEMALE_AVATAR = "avatar/newavatarfemale.jpg";

    public static String defaultAvatarHost = "https://avatar01.jiaoliuqu.com/";

    /**
     * 路径分隔符  "/"
     */
    public static final String SPT = "/";

    /**
     * http://
     */
    public static final String HTTP = "http://";

    /**
     * https://
     */
    public static final String HTTPS = "https://";

    /**
     * 获取存相册的url，无域名，无斜杠
     *
     * @param url
     * @return
     * <AUTHOR>
     * 2017年8月2日 下午12:06:12
     */
    public static String getAvatarOfSavePhoto(String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }

        try {
            URL _url = new URL(url);
            url = _url.getFile();
        } catch (Exception e) {
        }

        return removeSPT(url);
    }

    public static String removeSPT(String url) {
        while (url.startsWith(SPT)) {
            url = url.substring(1);
        }
        return url;
    }

    public static boolean isUrl(String url) {
        return url.startsWith(HTTP) || url.startsWith(HTTPS);
    }

    /**
     * 根据性别获取默认头像
     */
    public static String getDefAvatar(Integer gender) {
        return Objects.equals(gender, 2) ? DEFAULT_FEMALE_AVATAR : DEFAULT_MALE_AVATAR;
    }

    /**
     * 根据性别获取获取预设头像
     *
     * @param gender 性别，1:男; 2:女
     * @return
     * <AUTHOR>
     * 2017年3月9日 下午2:04:01
     */
    public static String getAvatarBySexType(Integer gender) {
        return defaultAvatarHost + getDefAvatar(gender);
    }

    public static String resolve(String avatar) {
        if (!AvatarTool.isUrl(avatar)) {
            return AvatarTool.defaultAvatarHost.concat(avatar);
        }
        return avatar;
    }

    public static String resolve(String avatar, String version) {
        if (StringUtils.isBlank(avatar)) {
            return avatar;
        }

        //判断
        if (StringUtils.isBlank(version) || Objects.equals("0", version)) {
            if(!isUrl(avatar)){
                //获取原路径
                avatar = defaultAvatarHost + removeSPT(avatar);
            }

            return avatar;
        }
        return getAvatarOfSavePhoto(avatar);
    }
}
