package cn.taqu.mp.util;

import org.apache.logging.log4j.util.Strings;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/3/1
 */
public class DateUtil {

    private static final String[] constellationArr = new String[]{"魔羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "魔羯座"};
    private static final int[] constellationEdgeDay = new int[]{20, 18, 20, 20, 20, 21, 22, 22, 22, 22, 21, 21};

    /**
     * 一分钟的秒数
     */
    public static final int ONE_MINUTE_MILL = 60000;

    /**
     * 一天的毫秒数
     */
    public static final int ONE_DAY_MILL = 86400000;

    /**
     * 一天的秒数
     */
    public static final int ONE_DAY_SECOND = 86400;

    /**
     * 8位时间格式：到天
     */
    private static final String DATE_LONG_DAY = "yyyyMMdd";

    /**
     * 返回时间格式携带横线
     */
    public static final String DATE_DAY_WITH_LINE = "yyyy-MM-dd";

    /**
     * 4位时间格式: 只有小时分钟 如 20:30
     */
    public static final String HOUR_MINIE = "HH:mm";

    /**
     * 时间格式化
     */
    public static final String DATE_TIME_20 = "yyyy-MM-dd HH:mm:ss";

    /**
     * 根据日期获取时间年
     *
     * @param date 当地时间
     * @return
     */
    public static int getYearByDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setMinimalDaysInFirstWeek(4);
        c.setTime(date);
        return c.getWeekYear();
    }

    /**
     * 根据日期获取日期一年中所在的周
     *
     * @param date
     * @return
     */
    public static int getWeekByDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setMinimalDaysInFirstWeek(4);
        c.setTime(date);
        return c.get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 根据日期获取日期一年中所在的月
     *
     * @param date
     * @return
     */
    private static int getMonthByDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * 根据日期获取所在周
     *
     * @param date
     * @return
     */
    public static String getWeekStr(Date date) {
        int year = getYearByDate(date);
        int week = getWeekByDate(date);
        if (week < 10) {
            return year + "0" + week;
        } else {
            return year + "" + week;
        }
    }

    /**
     * 根据日期获取所在天
     *
     * @param date
     * @return
     */
    public static String getDayStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    public static Integer getDt(Date date) {
        return Integer.parseInt(getDayStr(date));
    }

    public static Integer getDt(LocalDateTime localDateTime) {
        return Integer.parseInt(getDtStr(localDateTime));
    }

    public static String getDtStr(LocalDateTime localDateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        return dateTimeFormatter.format(localDateTime);
    }

    public static Integer getDt(Long time10) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String format = dateTimeFormatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(time10), ZoneId.systemDefault()));
        return Integer.parseInt(format);
    }

    /**
     * 根据日期获取时间戳格式字符串
     *
     * @param date
     * @return
     */
    public static String getTimestampStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(date);
    }

    /**
     * 根据日期获取所在月份
     *
     * @param date
     * @return
     */
    public static String getMonthStr(Date date) {
        int year = getYearByDate(date);
        int month = getMonthByDate(date);
        if (month < 10) {
            return year + "0" + month;
        } else {
            return year + "" + month;
        }
    }

    /**
     * 根据日期字符串
     *
     * @param date
     * @return
     */
    public static String getDateStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    public static String getWeekDayStr(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        switch (dayOfWeek) {
            case Calendar.SUNDAY:
                return "SUN";
            case Calendar.MONDAY:
                return "MON";
            case Calendar.TUESDAY:
                return "TUE";
            case Calendar.WEDNESDAY:
                return "WED";
            case Calendar.THURSDAY:
                return "THU";
            case Calendar.FRIDAY:
                return "FRI";
            case Calendar.SATURDAY:
                return "SAT";
            default:
                break;
        }
        return "";
    }

    /**
     * 获取某天的零点
     *
     * @param date
     * @return
     */
    public static Long getZero(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * 获取昨天开始的时间戳
     *
     * @return
     */
    public static Long getYesterdayStartTime() {
        return LocalDateTime.now().plusDays(-1).withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取明天天开始的时间戳
     *
     * @return
     */
    public static Long getTomorrowStartTime13() {
        return LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取明天天开始的时间戳
     *
     * @return
     */
    public static Long getTomorrowStartTime10() {
        return LocalDateTime.now().plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
    }

    /**
     * 获取今天开始的时间戳
     *
     * @return
     */
    public static Long getTodayStartTime10() {
        return LocalDateTime.now().plusDays(0).withHour(0).withMinute(0).withSecond(0).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
    }

    /**
     * 获取今天结束的时间戳
     *
     * @return
     */
    public static Long getTodayEndTime10() {
        return LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(0)
                .atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
    }

    /**
     * 获取本周开始时间毫秒级
     *
     * @param date
     * @return
     */
    public static Long minusDays(Date date) {
        LocalDateTime now = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        DayOfWeek dayOfWeek = now.getDayOfWeek();
        int value = dayOfWeek.getValue();

        LocalDateTime localDateTime = now.minusDays((value - 1));
        localDateTime = localDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 获取每月第一天
     *
     * @return
     */
    public static Long getFirstDayOfMonth(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.firstDayOfMonth());
        return firstDayOfWeek.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 获取每月最后一天
     *
     * @return
     */
    public static Long getEndDayOfMonth(LocalDate currentDate) {
        LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
        LocalTime lastTimeOfDay = LocalTime.MAX;
        LocalDateTime lastDateTimeOfMonth = lastDayOfMonth.atTime(lastTimeOfDay);
        return lastDateTimeOfMonth.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 获取每周第一天
     *
     * @param date 输入的日期
     * @return 返回每周第一天的毫秒时间戳（东八区）
     */
    public static Long getFirstDayOfWeek(LocalDate date) {
        LocalDate firstDayOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return firstDayOfWeek.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 获取每周最后一天
     *
     * @param date 输入的日期
     * @return 返回每周最后一天的毫秒时间戳（东八区）
     */
    public static Long getEndDayOfWeek(LocalDate date) {
        LocalDate lastDayOfWeek = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        LocalTime endOfDay = LocalTime.MAX;
        LocalDateTime lastDateTimeOfWeek = lastDayOfWeek.atTime(endOfDay);
        return lastDateTimeOfWeek.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 时间戳转8位时间格式(天)
     *
     * @param milli
     * @return
     */
    public static Long milliToLongDay(Long milli) {
        return Long.parseLong(milliToStringHandle(milli, DATE_LONG_DAY));
    }

    /**
     * @throws
     * @Title: dateToString10
     * @Description: (将如期格式化为10位的字符串形式 ， 如 ： 2011 - 04 - 15)
     */
    public static String dateToString10(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_DAY_WITH_LINE).format(date);
    }

    /**
     * 时间戳转4位时间格式: 只有小时分钟 如 20:30
     *
     * @param milli
     * @return
     */
    public static String milliToHourMinute(Long milli) {
        return milliToStringHandle(milli, HOUR_MINIE);
    }

    /**
     * 时间戳转时间格式模型
     *
     * @param milli
     * @param pattern
     * @return
     */
    public static String milliToStringHandle(Long milli, String pattern) {
        if (milli == null) {
            return Strings.EMPTY;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(milli), ZoneId.systemDefault());
        return dateTimeFormatter.format(localDateTime);
    }

    public static String dateToString20(Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(DATE_TIME_20).format(date);
    }

    /**
     * 获取距离下一小时的毫秒值
     *
     * @return
     */
    public static Long getHourEndTime() {
        LocalDateTime localDateTime = LocalDateTime.now().plusHours(1);
        return localDateTime.withMinute(0).withSecond(0).withNano(0).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 时间转换成秒
     *
     * @param timeString
     * @return
     */
    public static Long convertTimeToSeconds(String timeString) {
        String[] timeParts = timeString.split(":");
        if (timeParts.length != 3) {
            throw new RuntimeException("时间格式不正确，应为 hh:mm:ss");
        }

        int hours = Integer.parseInt(timeParts[0]);
        int minutes = Integer.parseInt(timeParts[1]);
        int seconds = Integer.parseInt(timeParts[2]);

        if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
            throw new RuntimeException("时间值超出有效范围");
        }
        return hours * 3600L + minutes * 60L + seconds;
    }

    /**
     * 秒换算成时间字符串
     *
     * @param seconds
     * @return
     */
    public static String convertSecondsToTime(Long seconds) {
        if (seconds < 0) {
            throw new RuntimeException("秒数不能为负数");
        }
        int hours = (int) (seconds / 3600);
        int remainingSeconds = (int) (seconds % 3600);
        int minutes = remainingSeconds / 60;
        int secs = remainingSeconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, secs);
    }

    /**
     * 计算时间戳距离所在天零点多少秒
     *
     * @param timestamp
     * @return
     */
    public static Long calculateSecondsSinceMidnight(Long timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long midnightTimestamp = calendar.getTimeInMillis();
        return (timestamp - midnightTimestamp) / 1000;
    }

    /**
     * 计算每日任务开始时间
     *
     * @param eventTs
     * @param offsetSeconds
     * @return
     */
    public static Long calculateStartTime(Long eventTs, Long offsetSeconds) {
        Long startTime = DateUtil.getZero(new Date(eventTs));
        Long realOffsetSeconds = calculateSecondsSinceMidnight(eventTs);
        if (realOffsetSeconds > offsetSeconds) {
            // 取当天
            return startTime + offsetSeconds * 1000;
        } else {
            // 取昨天
            return startTime + offsetSeconds * 1000 - ONE_DAY_MILL;
        }
    }

    /**
     * 判断是否同一天
     *
     * @param t1
     * @param t2
     * @return
     */
    public static boolean isSameDay(Long t1, Long t2) {
        return getDayStr(new Date(t1)).equals(getDateStr(new Date(t2)));
    }

    /**
     * 计算时区
     *
     * @param startTimeStamp
     * @param endTimeStamp
     * @return
     */
    public static List<Integer> calculateDtList(Long startTimeStamp, Long endTimeStamp) {
        List<Integer> dtList = new ArrayList<>();

        LocalDate startDate;
        LocalDate endDate;

        // startTime 和 endTime 都有值
        if (startTimeStamp != null && endTimeStamp != null) {
            startDate = Instant.ofEpochMilli(startTimeStamp).atZone(ZoneId.systemDefault()).toLocalDate();
            endDate = Instant.ofEpochMilli(endTimeStamp).atZone(ZoneId.systemDefault()).toLocalDate();
            // 添加从 startDate 到 endDate 的所有日期
            while (!startDate.isAfter(endDate)) {
                dtList.add(Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
                startDate = startDate.plusDays(1);
            }
        } else if (startTimeStamp == null && endTimeStamp == null) {
            // startTime 和 endTime 都没有值，默认取当天
            startDate = endDate = LocalDate.now();
            dtList.add(Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        } else {
            // startTime 或 endTime 其中一个没有值，取有值的那一天
            Instant instant = Instant.ofEpochMilli((startTimeStamp != null) ? startTimeStamp : endTimeStamp);
            startDate = endDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
            dtList.add(Integer.parseInt(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        }

        return dtList;
    }

    public static Date fromSecond(long second) {
        Date date = new Date(second * 1000);
        return date;
    }

    public static String getConstellationFromSeconds(Long seconds) {
        if (Objects.equals(seconds, 0L)) {
            return "";
        }

        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.setTimeInMillis(seconds * 1000L);
        int month = cal.get(2) + 1;
        int day = cal.get(5);
        if (day <= constellationEdgeDay[month - 1]) {
            --month;
        }

        return month >= 0 ? constellationArr[month] : constellationArr[11];
    }
}
