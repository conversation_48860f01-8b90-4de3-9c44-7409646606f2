package cn.taqu.mp.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/16 下午3:47
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserLocationInfoVo implements Serializable {

    public static UserLocationInfoVo UNKNOWN = new UserLocationInfoVo();

    private Double latitude;

    private Double longitude;

    private String ipPro;

    private String ipCity;

    private Long cityId;

}
