package cn.taqu.mp.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户地址信息VO
 * 
 * <AUTHOR>
 * @date 2025/7/18
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserAddressInfoVo implements Serializable {

    public static final UserAddressInfoVo UNKNOWN = new UserAddressInfoVo();

    /**
     * 城市ID
     */
    private Long city;
    
    /**
     * 城市名称
     */
    private String cityName;
    
    /**
     * 省份ID
     */
    private Long province;
    
    /**
     * 省份名称
     */
    private String provinceName;
    
    /**
     * 经度
     */
    private String longitude;
    
    /**
     * 纬度
     */
    private String latitude;
}
