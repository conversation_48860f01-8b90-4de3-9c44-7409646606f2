package cn.taqu.mp.factory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.*;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/2/17 下午6:30
 */
@Slf4j
@RequiredArgsConstructor
public final class PulsarFactory {

    private final PulsarClient client;

    public Producer<String> producer(String topic) {
        try {
            return client.newProducer(Schema.STRING)
                .topic(topic)
                .create();
        } catch (PulsarClientException e) {
            throw new UnsupportedOperationException("Pulsar producer not initialized.", e);
        }
    }

    public Consumer<String> consumer(String topic) {
        try {
            return client.newConsumer(Schema.STRING)
                .topic(topic)
                .subscriptionName("mp-activity")
                .subscriptionType(SubscriptionType.Shared)
                .negativeAckRedeliveryDelay(1, TimeUnit.SECONDS)
                .subscribe();
        } catch (PulsarClientException e) {
            throw new UnsupportedOperationException("Pulsar consumer not initialized.", e);
        }
    }

    public void subscribe(MessageListener<String> messageListener, String... topic) {
        client.newConsumer(Schema.STRING)
            .topic(topic)
            .subscriptionName("mp-user-center")
            .subscriptionType(SubscriptionType.Shared)
            .messageListener(messageListener)
            .negativeAckRedeliveryDelay(1, TimeUnit.SECONDS)
            .subscribeAsync().whenComplete((c, e) -> {
                if (e != null) {
                    String err = "Unable to sub " + Arrays.toString(topic);
                    log.error(err, e);
                }
            });
    }
}
