package cn.taqu.mp.constant;

import cn.taqu.mp.dto.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Set;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/4/18 上午10:12
 */
@RequiredArgsConstructor
public enum UserInfoCategory {

    /**
     * 骨架信息
     */
    SKELETON(UserSkeletonInfoDto.class),

    /**
     * 核心信息
     */
    CORE(UserCoreInfoDto.class),
    /**
     * 定位信息
     */
    LOCATION(UserLocationInfoDto.class),
    /**
     * 卡片信息
     */
    CARD(UserCardInfoDto.class),
    /**
     * 相册信息 or 我的生活
     */
    ALBUM(UserAlbumInfoDto.class),
    /**
     * 学校信息
     */
    SCHOOL(UserSchoolInfoDto.class),
    /**
     * 理想型
     */
    IDEAL_TARGET(UserIdealTargetDto.class),
    /**
     * 认证信息
     */
    CERTIFICATION(UserCertificationInfoDto.class),

    /**
     * 手机号相关
     */
    MOBILE(MobileDto.class),

    /**
     * 地址信息
     */
    ADDRESS(UserAddressInfoDto.class);

    @Getter
    private final Class<? extends Categorized> type;

    public static UserInfoCategory typeOf(Class<?> t) {
        return Stream.of(UserInfoCategory.values()).filter(e -> e.type.equals(t)).findAny().orElseThrow(IllegalArgumentException::new);
    }

}
