package cn.taqu.mp.constant;

import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/3/28 下午3:50
 */
@RequiredArgsConstructor
public enum Gender {

    UNKNOWN(0),
    MALE(1),
    FEMALE(2);

    private final Integer val;

    public Integer val() {
        return val;
    }

    public boolean match(Integer gender) {
        return Objects.equals(this.val, gender);
    }
}
