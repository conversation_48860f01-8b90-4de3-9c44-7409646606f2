package cn.taqu.mp.constant;

public enum LiveFaceDetectStatus {

    /**
     * 未认证 0
     */
    UNAUTHORIZED(0),
    /**
     * 认证通过 1
     */
    AUTHORIZED(1),
    /**
     * 照片对比认证失败 2
     */
    AUTHORIZED_FAIL(2),
    /**
     * 认证中，认证中间状态，没有存到数据库或者缓存 3
     */
    AUTHORIZING(3),
    /**
     * 取消认证 4
     */
    CANCEL_AUTHORIZING(4),
    /**
     * 活体认证失败 5
     */
    FACE_AUTHORIZED_FAIL(5),
    /**
     * 人工认证（针对照片对比失败的状态进行人工认证）6
     */
    MANUAL_AUTHORIZED(6);

    private byte status;

    LiveFaceDetectStatus(int status){
        this.status = (byte) status;
    }

    public byte getStatus() {
        return status;
    }
}
