package cn.taqu.mp.constant;

/**
 * info/getInfoByUuid 接口可支持的字段</br>
 * 只有该接口相关的业务才可使用此类</br>
 *
 * <AUTHOR>
 * 2024年3月6日下午3:10:29
 */
public interface UserInfoField {

    String MOBILE = "mobile"; // 手机号
    String IS_BIND_MOBILE = "is_bind_mobile";
    String MOBILE_CIPHER = "mobile_cipher"; // 手机号加密
    // 2024.05.24 优化使用 mobile来判断
    String ACCOUNT_NAME = "account_name";//缓存不存在的默认显示用户名为游客
    String ACCOUNT_NAME_ORIGIN = "account_name_origin";
    String ACCOUNT_NAME_STATUS = "account_name_status";
    String ACCOUNT_STATUS = "account_status";
    String ACCOUNT_TYPE = "account_type";//缓存不存在的默认当游客处理
    String SEX_TYPE = "sex_type"; // 性别
    String AVATAR = "avatar"; // 头像
    String AVATAR_ORIGIN = "avatar_origin";//原始头像，头像还未审核时，Avatar为空，这些用户查看自己的信息时，返回该字段
    String UUID = "uuid"; // uuid
    String CREATE_TIME = "create_time"; // 创建时间/注册时间
    String MEMBER_ID = "member_id"; // 设备表id
    String AVATAR_STATUS = "avatar_status";//头像状态 0:未审核; 1:已审核
    String REG_APPCODE = "reg_appcode";//注册时的appcode
    String REG_CLONED = "reg_cloned";//注册时的分身版
    String REG_PLATFORM = "reg_platform";//注册时的平台
    String REG_CHANNEL = "reg_channel";//注册时的渠道
    String REG_STYLE = "reg_style";//注册方式
    String CONSTELLATION = "constellation"; //星座

    /** 用户头像是否进行阿里云人脸对比认证 **/
    // use real_person_certification instead
    String PRE_PROFILE_VERIFY_STATUS = "pre_profile_verify_status"; //审核通过前的头像认证状态，0-未认证，1-认证成功，2-认证失败
    String PRE_PROFILE_ERROR_MSG = "pre_profile_error_msg";  //审核通过前的头像认证失败的原因

    // use real_person_certification instead
    String PROFILE_VERIFY_STATUS = "profile_verify_status"; //头像认证状态，0-未认证，1-认证成功，2-认证失败
    String PROFILE_ERROR_MSG = "profile_error_msg";  //头像认证失败的原因

    /** 真人认证状态
     *  名称解释
     *  机审：图片机器自动审核
     *  非真人认证头像人审：从未通过真人认证的用户，传头像都在此审核。
     *  真人认证头像人审：用户活体认证通过，切审核通过，则之后修改头像，均提交到真人认证头像人审。
     *
     *  正常真人认证流程，用户同一张头像图会经历，均通过后，才标记真人认证 机审-> 非真人认证头像人审-> 真人认证头像人审。
     **/
    /*
     *  该字段解释为：首次真人认证审核状态标记。只有非真人认证头像人审通过时，用户已活体且头像与底图相似。才会比较审核状态为1。
     *  20240910有发现bug，用户已经完整真人认证，但是该字段仍然为0。原因是用户与审核人员并发操作导致
     */
    String REAL_PERSON_CERTIFICATION_STATUS = "real_person_certification_status"; // 真人认证审核 -1-未审核/审核失败 0审核中 1-审核成功
    String REAL_PERSON_CERTIFICATION = "real_person_certification"; // -1-未认证，1-认证成功，2-认证失败
    String AVATAR_SAME_WITH_FACE = "avatar_same_with_face"; // 头像是否与活体一致 0-未认证，1-认证成功

    /** 用户头像是否进行阿里云人像认证 **/
    String PRE_AVATAR_FACE_STATUS = "pre_avatar_face_status"; //主态，审核通过前的头像人像检验状态，0-未认证，1-认证成功，2-认证失败
    String AVATAR_FACE_STATUS = "avatar_face_status"; //客态，头像人像检验状态，0-未认证，1-认证成功，2-认证失败

    /* **************来自于accounts_forum_profile表的数据***************/
    String AGE = "age"; // 年龄
    String BASEADDR = "baseaddr"; // 所在地
    String HOMETOWN = "hometown"; // 家乡
    String BIRTH = "birth"; // 生日
    String PERSONAL_PROFILE_BE_ALLOW = "personal_profile_be_allow";
    String DATING_INTENTION = "dating_intention";
    String PERSONAL_PROFILE = "personal_profile"; // 个人简介
    String PERSONAL_PROFILE_STATUS = "personal_profile_status";
    String GENDER_CERTIFICATION = "gender_certification";//性别认证 现在就是实名认证 对于 zhima_certification
    String ZHIMA_CERTIFICATION = "zhima_certification";//芝麻认证 现在就是实名认证
    String ZHIMA_CERTIFICATION_HISTORY = "zhima_certification_history";//历史芝麻认证
    String VOICE_CERTIFICATION = "voice_certification";//语音认证
    String FACE_CERTIFICATION = "face_certification";//阿里云人脸活体认证

    String CHAT_REAL_CERTIFICATION = "chat_real_certification"; // 业务级实名认证
    String REGISTER_AVATAR_STATUS = "register_avatar_status"; //注册初始头像是否违规，默认为0；0：不违规，1：违规'

    //个人简介审核通过后存储到这里
    String PASS_PERSONAL_PROFILE = "pass_personal_profile";
    String PASS_PERSONAL_PROFILE_STATUS = "pass_personal_profile_status";
    //20200312新增字段
    String EDUCATION = "education"; //学历  对应数据库 education_level
    String TRADE = "trade"; //行业
    String PROFESSION = "profession"; //职业
    String HEIGHT = "height"; //身高，用户未填写时，默认为0
    String WEIGHT = "weight"; //体重，用户未填写时，默认为0
    String INCOME = "income"; //收入，0：未填写，1：5万以下，2：5～10万，3：10～20万，4：20～30万， 5：30～50万，6：50～100万，7：100万以上

    /* **************来自于accounts_member_info表的数据***************/
    String LOGIN_APPCODE = "login_appcode";//最后登录的appcode，即当前appcode
    String LOGIN_CLONED = "login_cloned";//最后登录的cloned，即当前cloned
    String APP_VERSION = "app_version";//最后使用的app版本(和设备相关)
    String PLATFORM_ID = "platform_id";//最后使用的平台
    String CHANNEL = "channel";//最后使用的平台

    /* **************来自于accounts_card表的数据***************/
    String ACCOUNT_CARD_ID = "account_card_id";//用户佩戴的靓号id
    String ACCOUNT_CARD_LEVEL = "account_card_level";//用户佩戴的靓号等级
    String DEFAULT_CARD_ID = "default_card_id";//用户默认靓号

    /* ************** 未存表 ***************/
    String LONGITUDE = "longitude";// 经度 消费队列同步来
    String LATITUDE = "latitude";// 纬度 消费队列同步来
    String CITY_ID = "city_id";// 城市id 消费队列同步来
    String LOCATION_CITY_ID = "cityId";
    String LAST_APP_VERSION = "last_app_version";//最后登录的app版本（和用户相关）（如果app包升级，用户未重新登录，数据不会变化）
    String LAST_ACTIVE_APP_VERSION = "last_active_app_version";//最后活跃使用的平台（和用户相关） - 订阅网关心跳
    String LAST_PLATFORM_ID = "last_platform_id";//最后使用的平台（和用户相关）
    String ENABLE_LOCATION = "enable_location"; //用户手机是否启用定位服务，0-未启用，1-启用

    /* ************** 来自于voice_sign_info表 ***************/

    // 主态
    String VOICE_SIGN = "voice_sign";// 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
    String VOICE_SIGN_URL = "voice_sign_url";// 语音签名url
    String VOICE_SIGN_DURATION = "voice_sign_duration";//语音签名时长
    String VOICE_SIGN_AUDIT_TIME = "voice_sign_audit_time";//语音签名审核时间
    String VOICE_SIGN_AUDIT_REASON = "voice_sign_audit_reason";//语音签名审核原因

    // 客态
    String PASS_VOICE_SIGN = "pass_voice_sign";// 语音签名状态，0-审核中，1-审核通过，2-审核拒绝
    String PASS_VOICE_SIGN_URL = "pass_voice_sign_url";// 审核通过的语音签名url
    String PASS_VOICE_SIGN_DURATION = "pass_voice_sign_duration";//审核通过的语音签名时长
    String PASS_VOICE_SIGN_AUDIT_TIME = "pass_voice_sign_audit_time";//审核通过的语音签名审核时间
    String PASS_VOICE_SIGN_AUDIT_REASON = "pass_voice_sign_audit_reason";//审核通过的语音签名审核原因
    String VOICE_SIGN_PRE_URL = "voice_sign_pre_url";  //语音签名前缀

    String IP_PRO = "ip_pro";  // ip解析的省份
    String IP_CITY = "ip_city"; // ip解析的城市

    /* ************** 来自于accounts_introduction 表 ***************/
    // 主态
    String MY_INTRODUCTION_STATUS = "my_introduction_status";// 自我介绍状态，0-审核中，1-审核通过，2-审核拒绝
    String MY_INTRODUCTION_CONTENT = "my_introduction_content";// 自我介绍内容
    String MY_INTRODUCTION_IMGS = "my_introduction_imgs";// 自我介绍图片

    // 客态
    String PASS_INTRODUCTION_STATUS = "pass_introduction_status";// 通过的自我介绍状态，0-审核中，1-审核通过，2-审核拒绝
    String PASS_INTRODUCTION_CONTENT = "pass_introduction_content";// 通过的自我介绍内容
    String PASS_INTRODUCTION_IMGS = "pass_introduction_imgs";// 通过的自我介绍图片

    /** 身份证生日 **/
    String IDENTITY_NO_BIRTH = "identity_no_birth";


    /** 用户活跃 **/
    String ACTIVE_TIME = "active_time";
    String PRE_ACTIVE_TIME = "pre_active_time";

    /** 所在地城市 **/
    String BASEADDR_CITY = "baseaddr_city";

    /** 所在地城市 **/
    String HOMETOWN_CITY = "hometown_city";

    /** 用户相册 **/
    String PHOTO = "photo";
    // 真人相册
    String REAL_PHOTO_CERTIFICATION = "real_photo_certification";  //是否包含真人认证照片，0-不包含，1-包含
    // 真人头像
    String REAL_AVATAR_CERTIFICATION = "real_avatar_certification";  //是否真人头像(真人认证通过且头像为有效照片)，0-不是，1-是

    /** 我的生活 **/
    String ACCOUNTS_LIFE_LIST = "accounts_life_list";
}
