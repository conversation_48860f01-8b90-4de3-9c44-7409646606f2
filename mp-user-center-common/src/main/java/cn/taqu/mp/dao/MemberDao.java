package cn.taqu.mp.dao;

import cn.taqu.mp.dto.MemberDto;
import cn.taqu.mp.entity.Member;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.NativeQuery;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/3/4 下午1:38
 */
public interface MemberDao extends JpaRepository<Member, Long> {
    /**
     * id批量
     * @param ids
     * @return
     */
    @NativeQuery("SELECT id, token FROM members WHERE id IN :ids")
    List<MemberDto> findByIdIn(Collection<Long> ids);
}
