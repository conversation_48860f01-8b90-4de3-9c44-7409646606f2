package cn.taqu.mp.dao;

import cn.taqu.mp.dto.PhotoInfoDto;
import cn.taqu.mp.entity.AccountsLife;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.NativeQuery;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 上午9:46
 */
public interface AccountsLifeDao extends JpaRepository<AccountsLife, Long> {

    @NativeQuery("SELECT account_uuid, photo_url, width, height, seq, status, verify_status, remark FROM accounts_life WHERE account_uuid IN :uuids AND status in (1,3)")
    List<PhotoInfoDto> listPassedLife(Collection<String> uuids);


}
