package cn.taqu.mp.dao;

import cn.taqu.mp.entity.AccountLabel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30 14:28
 */
public interface AccountLabelDao extends JpaRepository<AccountLabel, Long> {

    /**
     * 获取所有启用状态的配置
     *
     * @param accountUuids
     * @return
     */
    List<AccountLabel> findByAccountUuidIn(List<String> accountUuids);

}
