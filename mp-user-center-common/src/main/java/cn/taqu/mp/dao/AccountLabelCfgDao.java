package cn.taqu.mp.dao;

import cn.taqu.mp.entity.AccountLabelCfg;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.NativeQuery;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/30 14:28
 */
public interface AccountLabelCfgDao extends JpaRepository<AccountLabelCfg, Long> {

    /**
     * 获取最新活跃的一条数据
     *
     * @return
     */
    @NativeQuery("SELECT modify_time from account_label_cfg order by modify_time desc limit 1")
    Timestamp findLastTime();

    /**
     * 获取所有启用状态的配置
     *
     * @param status
     * @return
     */
    List<AccountLabelCfg> findByDataStatus(Integer status);

}
