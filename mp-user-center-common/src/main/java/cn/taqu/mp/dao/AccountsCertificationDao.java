package cn.taqu.mp.dao;

import cn.taqu.mp.entity.AccountsCertification;
import cn.taqu.mp.entity.School;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 下午3:36
 */
public interface AccountsCertificationDao extends JpaRepository<AccountsCertification, Long> {

    List<AccountsCertification> findByAccountUuidIn(Collection<String> uuids);

}
