package cn.taqu.mp.dao;

import cn.taqu.mp.dto.PhotoInfoDto;
import cn.taqu.mp.entity.AccountsPhoto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.NativeQuery;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 上午9:46
 */
public interface AccountsPhotoDao extends JpaRepository<AccountsPhoto, Long> {

    @NativeQuery("SELECT account_uuid, photo_url, seq FROM accounts_photo WHERE account_uuid IN :uuids AND status in (2,3,4,7) AND seq > 1")
    List<PhotoInfoDto> listPassedPhoto(Collection<String> uuids);

    @NativeQuery("SELECT account_uuid, photo_url, seq FROM accounts_photo WHERE account_uuid IN :uuids AND status = 2 AND verify_status = 1 AND seq = 1")
    List<PhotoInfoDto> listRealPersonAvatar(Collection<String> uuids);

}
