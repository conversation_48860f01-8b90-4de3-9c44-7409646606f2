package cn.taqu.mp.dao;

import cn.taqu.mp.entity.AccountsIdealTarget;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 下午4:25
 */
public interface AccountsIdealTargetDao extends JpaRepository<AccountsIdealTarget, Long> {

    List<AccountsIdealTarget> findByAccountUuidIn(Collection<String> uuids);
}
