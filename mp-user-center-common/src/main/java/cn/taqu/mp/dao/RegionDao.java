package cn.taqu.mp.dao;

import cn.taqu.mp.entity.Region;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.NativeQuery;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 下午1:38
 */
public interface RegionDao extends JpaRepository<Region, Long> {
    /**
     * id批量
     *
     * @param ids
     * @return
     */
    @NativeQuery("SELECT id, sign, parent_id, region_path, level, region_name, is_unavailable, sort, postcode, longitude, latitude FROM region WHERE id IN :ids")
    List<Region> findByIdIn(Collection<Long> ids);

}
