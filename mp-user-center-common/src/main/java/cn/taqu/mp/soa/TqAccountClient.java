package cn.taqu.mp.soa;

import cn.taqu.etcd.annotation.EtcdValue;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.dto.UserLocationInfoDto;
import cn.taqu.soa.common.client.SoaClient;
import cn.taqu.soa.common.client.SoaResponse;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/23 下午5:14
 */
@Component
public class TqAccountClient {

    @EtcdValue("/service/java/account")
    private String host;

    @Resource
    private SoaClient client;

    @Resource
    private MappingKeyScanner keyScanner;

    public Map<String, UserLocationInfoDto> getCertificationInfo(List<String> uuids) {
        SoaResponse resp = client.call(host, "info", "getInfoByUuid", uuids, keyScanner.fields(UserLocationInfoDto.class));
        if (resp.fail()) {
            return Map.of();
        }
        return JsonUtils.stringToObject(resp.getData(), new TypeReference<>() {});
    }
}
