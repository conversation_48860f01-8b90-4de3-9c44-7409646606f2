package cn.taqu.mp.soa;

import cn.taqu.etcd.annotation.EtcdValue;
import cn.taqu.mp.dto.UserLocationInfoDto;
import cn.taqu.soa.common.client.SoaClient;
import cn.taqu.soa.common.client.SoaResponse;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 加密
 *
 * <AUTHOR>
 * @date 2025/4/29 11:46
 */
@Slf4j
@Component
public class EncryptClient {

    public static final String ACCOUNT_CODE = "account";

    @EtcdValue("/service/go/encrypt")
    private String host;

    @Resource
    private SoaClient client;

    public Map<String, String> batchDecrypt(Map<String, String> content) {
        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isEmpty(content)) {
            return result;
        }

        Object[] form = {ACCOUNT_CODE, new Object[]{content}};
        SoaResponse resp = client.call(host, "operation", "batchDecrypt", form);
        if (resp.fail()) {
            log.warn("go batchDecrypt error:{}, req:{}", resp.getMsg(), JsonUtils.objectToString(content));
            return result;
        }

        List<Map<String, String>> resData = JsonUtils.stringToObject2(resp.getData(), new TypeReference<>() {
        });
        if (CollectionUtils.isNotEmpty(resData)) {
            return resData.get(0);
        }
        return result;
    }
}
