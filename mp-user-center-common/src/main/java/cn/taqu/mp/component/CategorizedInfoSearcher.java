package cn.taqu.mp.component;

import cn.taqu.mp.dto.Categorized;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/18 上午11:11
 */
public interface CategorizedInfoSearcher<T extends Categorized> {

    /**
     * 类所有字段检索
     *
     * @param myUuid
     * @param uuids
     * @return
     */
    Map<String, ? extends Serializable> search(String myUuid, List<String> uuids);

    /**
     * 检索查询相关字段, 过滤多余字段需要重写search
     *
     * @param uuids
     * @return
     */
    default Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        var map = search("", uuids);
        return JsonUtils.mapper().convertValue(map, new TypeReference<>() {});
    }

}
