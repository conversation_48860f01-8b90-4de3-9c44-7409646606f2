package cn.taqu.mp.component;

import cn.taqu.mp.dto.Categorized;

import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 快速检索，按key合并后再查询
 * <AUTHOR>
 * @date 2025/4/18 上午11:11
 */
public interface FastInfoSearcher<T extends Categorized> extends CategorizedInfoSearcher<T> {

    /**
     * 过滤可用字段
     * @param fields
     * @return
     */
    Set<String> filterFields(Collection<String> fields);

    void postSearch(Map<String, Map<String, String>> infoMap, Set<String> filteredFields);

}
