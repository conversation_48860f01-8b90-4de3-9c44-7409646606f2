package cn.taqu.mp.component;

import cn.hutool.core.collection.CollectionUtil;
import cn.taqu.mp.constant.UserInfoCategory;
import cn.taqu.mp.dto.Categorized;
import cn.taqu.mp.util.Reflections;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.*;

/**
 * 模块化聚合查询
 * <AUTHOR>
 * @date 2025/4/18 上午10:58
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserInfoAggregator implements BeanPostProcessor {

    private ObjectMapper                                           objectMapper;

    private final Executor                                             executor = Executors.newVirtualThreadPerTaskExecutor();

    private final Map<UserInfoCategory, CategorizedInfoSearcher<?>> searcherMap = new HashMap<>(4);

    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        if (bean instanceof FastInfoSearcher<?> searcher) {
            Class<?> type = Reflections.genericType(searcher.getClass(), FastInfoSearcher.class, 0);
            UserInfoCategory category = UserInfoCategory.typeOf(type);
            searcherMap.put(category, searcher);
        } else if (bean instanceof CategorizedInfoSearcher<?> searcher) {
            Class<?> type = Reflections.genericType(searcher.getClass(), CategorizedInfoSearcher.class, 0);
            UserInfoCategory category = UserInfoCategory.typeOf(type);
            searcherMap.put(category, searcher);
        }
        if (bean instanceof ObjectMapper om) {
            this.objectMapper = om;
        }
        return bean;
    }

    /**
     * 平铺字段
     * @param uuids
     * @param categories
     * @return
     */
    public Map<String, Map<String, Object>> flatAggregate(String myUuid, List<String> uuids, UserInfoCategory... categories) {
        Map<String, Map<String, Object>> result = new HashMap<>(uuids.size());
        // 虚拟线程并发查询
        List<CompletableFuture<Void>> futures = new ArrayList<>(categories.length);
        for (var category : categories) {
            var future = CompletableFuture.runAsync(() -> {
                CategorizedInfoSearcher<?> searcher = searcherMap.get(category);
                Map<String, ? extends Serializable> categoryMap = searcher.search(myUuid, uuids);
                categoryMap.forEach((uuid, dto) ->
                    result.compute(uuid, (u, v) -> {
                        if (v == null) {
                            return objectMapper.convertValue(dto, new TypeReference<>() {});
                        } else {
                            Map<String, Object> info = objectMapper.convertValue(dto, new TypeReference<>() {});
                            merge(v, info, category, Set.of());
                            return v;
                        }
                    })
                );
            }, executor).exceptionally(e -> {
                e = e instanceof CompletionException ce ? ce.getCause() : e;
                String msg = "用户模块信息查询失败" + category;
                log.warn(msg, e);
                return null;
            });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();
        return result;
    }

    /**
     * 平铺字段过滤
     *
     * @param uuids
     * @param categories
     * @param fields
     * @return
     */
    public Map<String, Map<String, String>> flatAggregate(List<String> uuids, List<UserInfoCategory> categories, Collection<String> fields) {
        Map<String, Map<String, String>> result = new ConcurrentHashMap<>(uuids.size());
        // 虚拟线程并发查询
        List<CompletableFuture<Void>> futures = new ArrayList<>(categories.size());
        for (var category : categories) {
            var future = CompletableFuture.runAsync(() -> {
                CategorizedInfoSearcher<?> searcher = searcherMap.get(category);
                Map<String, Map<String, String>> map = searcher.search(uuids, fields);
                map.forEach((uuid, info) ->
                    result.compute(uuid, (k, v) -> {
                        if (v == null) {
                            return info;
                        } else {
                            merge(v, info, category, fields);
                            return v;
                        }
                    })
                );
            }, executor).exceptionally(e -> {
                e = e instanceof CompletionException ce ? ce.getCause() : e;
                String msg = "用户模块信息查询失败" + category;
                log.warn(msg, e);
                return null;
            });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();
        return result;
    }

    public <T> void merge(Map<String, T> target, Map<String, T> source, UserInfoCategory category, Collection<String> fields) {
        Class<? extends Categorized> type = category.getType();
        Set<String> compromiseFields = MappingKeyScanner.me().compromiseFields(type);
        source.forEach((k, v) -> {
            if (CollectionUtil.isEmpty(fields) || fields.contains(k)) {
                if (compromiseFields.contains(k)) {
                    // 预览字段，冲突处理
                    target.putIfAbsent(k, v);
                } else {
                    // 非预览字段，直接覆盖
                    target.put(k, v);
                }
            }
        });
    }

}