package cn.taqu.mp.component;

import cn.hutool.core.lang.ClassScanner;
import cn.taqu.mp.annotation.SearchIgnore;
import cn.taqu.mp.annotation.Compromise;
import cn.taqu.mp.dto.MappingKey;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/18 上午10:26
 */
@Component
public class MappingKeyScanner implements InitializingBean {

    private final Map<Class<?>, Set<String>>           fieldMap = new HashMap<>(8);

    private final Map<Class<?>, Set<String>> compromiseFieldMap = new HashMap<>(4);

    private static MappingKeyScanner                       self;

    private final ObjectMapper                     objectMapper;

    public MappingKeyScanner(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        self = this;
    }

    public static MappingKeyScanner me() {
        return self;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        var classes = ClassScanner.scanPackageBySuper("cn.taqu.mp.dto", MappingKey.class);
        classes.forEach(type -> {
            BeanDescription beanDesc = objectMapper.getSerializationConfig().introspect(
                TypeFactory.defaultInstance().constructType(type)
            );
            beanDesc.findProperties().forEach(bpd -> {
                if (bpd.getField().hasAnnotation(Compromise.class)) {
                    compromiseFieldMap.computeIfAbsent(type, k -> new HashSet<>()).add(bpd.getName());
                }
                if (!bpd.getField().hasAnnotation(SearchIgnore.class)) {
                    fieldMap.computeIfAbsent(type, k -> new HashSet<>()).add(bpd.getName());
                }
            });
        });
    }

    public Set<String> fields(Class<?> type) {
        return fieldMap.get(type);
    }

    public Set<String> compromiseFields(Class<?> type) {
        return compromiseFieldMap.getOrDefault(type, Set.of());
    }
}

