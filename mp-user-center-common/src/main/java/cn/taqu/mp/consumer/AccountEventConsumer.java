//package cn.taqu.mp.consumer;
//
//import cn.taqu.core.utils.Identities;
//import cn.taqu.etcd.annotation.EtcdValue;
//import cn.taqu.mp.dto.AccountEventDto;
//import cn.taqu.mp.factory.PulsarFactory;
//import cn.taqu.soa.utils.JsonUtils;
//import cn.taqu.soa.web.protocol.SoaBaseParams;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.pulsar.client.api.Consumer;
//import org.apache.pulsar.client.api.Message;
//import org.redisson.api.RAtomicLong;
//import org.redisson.api.RedissonClient;
//import org.springframework.stereotype.Component;
//
//import java.time.Duration;
//
///**
// * <AUTHOR>
// * @date 2025/1/24 下午2:08
// */
//@Slf4j
//@Component
//public final class AccountEventConsumer extends AbstractPulsarConsumer {
//
//    private final RedissonClient redissonBizClient;
//
//    public AccountEventConsumer(RedissonClient redissonBizClient, PulsarFactory pulsarFactory) {
//        super(pulsarFactory, "persistent://middle_tenant/mp_ns/mp_user_center_event");
//        this.redissonBizClient = redissonBizClient;
//    }
//
//    /**
//     * 不超过cpu - 1，超出不生效
//     */
//    @EtcdValue(value = "storage/pulsar/account-event-threads", defaultValue = "1")
//    public void setThreads(Integer threads) {
//        super.threads = threads;
//    }
//
//    @Override
//    public void onMessage(Consumer<String> c, Message<String> m) {
//        SoaBaseParams baseParams = SoaBaseParams.fromThread();
//        baseParams.setDistinctRequestId(Identities.uuid2());
//        log.info("account event arrived {}, {}", m.getValue(), m.getEventTime());
//        AccountEventDto dto = JsonUtils.stringToObject(m.getValue(), AccountEventDto.class);
//        String key = "acc:event:watermark:%s:%s".formatted(dto.accountUuid(), dto.scene());
//        RAtomicLong watermark = redissonBizClient.getAtomicLong(key);
//        long wts = watermark.get();
//        if (m.getEventTime() <= wts) {
//            log.warn("过期数据，跳过");
//            ack(c, m);
//            return;
//        }
//        boolean cas = watermark.compareAndSet(wts, m.getEventTime());
//        if (!cas) {
//            log.warn("过期数据，跳过");
//            ack(c, m);
//            return;
//        }
//        watermark.expireAsync(Duration.ofMinutes(1));
//        // todo 更新
//        log.info("数据 {}", m.getValue());
//        ack(c, m);
//    }
//
//
//
//}
