package cn.taqu.mp.service;

import cn.taqu.etcd.annotation.EtcdValue;
import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.component.FastInfoSearcher;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.constant.EnableStatus;
import cn.taqu.mp.constant.UserInfoField;
import cn.taqu.mp.dto.MobileDto;
import cn.taqu.soa.common.client.SoaClient;
import cn.taqu.soa.common.client.SoaResponse;
import cn.taqu.soa.utils.JsonUtils;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;

/**
 * 手机号
 *
 * <AUTHOR>
 * @date 2025/4/28 下午4:14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MobileService implements FastInfoSearcher<MobileDto> {

    @EtcdValue("/service/java/web-account")
    private String host;

    @Resource
    private UserInfoService infoService;

    private static final String MOBILE_ACCURATE = "mobile_accurate";

    private final SoaClient client;

    @Override
    public Map<String, ? extends Serializable> search(String myUuid, List<String> uuids) {
        List<String> uuid = Lists.newArrayList(uuids);
        uuid.add(myUuid);
        Map<String, Map<String, String>> process = process(uuid);
        return JsonUtils.mapper().convertValue(process, new TypeReference<Map<String, MobileDto>>() {});
    }

    @Override
    public Set<String> filterFields(Collection<String> fields) {
        Set<String> result = new HashSet<>(2);
        if (fields.contains(UserInfoField.MOBILE)) {
            log.warn("mobile sensitive query!");
            result.add(MOBILE_ACCURATE);
        }
        if (fields.contains(UserInfoField.IS_BIND_MOBILE)) {
            result.add(UserInfoField.MOBILE);
            result.add(UserInfoField.IS_BIND_MOBILE);
        }
        return result;
    }

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        Set<String> targetFields = filterFields(fields);
        if (targetFields.isEmpty()) {
            return new HashMap<>(2);
        }

        Map<String, Map<String, String>> result = infoService.query(uuids, targetFields);
        postSearch(result, targetFields);
        return result;
    }

    @Override
    public void postSearch(Map<String, Map<String, String>> infoMap, Set<String> filteredFields) {
        if (filteredFields.contains(MOBILE_ACCURATE)) {
            // 查j70
            var mobileMap = findMobile(infoMap.keySet());
            infoMap.forEach((uuid, info) -> {
                String mobile = mobileMask(mobileMap.getOrDefault(uuid, ""));
                info.put(UserInfoField.MOBILE, mobile);
                info.put(UserInfoField.IS_BIND_MOBILE, StringUtils.isNotBlank(mobile) ? "1" : "0");
            });
        } else {
            infoMap.forEach((uuid, info) -> {
                String mobile = info.get(UserInfoField.MOBILE);
                info.put(UserInfoField.IS_BIND_MOBILE, StringUtils.isNotBlank(mobile) ? "1" : "0");
            });
        }
    }

    private Map<String, Map<String, String>> process(List<String> uuids) {
        Map<String, Map<String, String>> result = Maps.newHashMap();
        Map<String, String> mobiles = findMobile(uuids);
        uuids.forEach(uuid -> {
            Map<String, String> mobileEntity = Maps.newHashMapWithExpectedSize(2);
            String value = mobiles.getOrDefault(uuid, EnableStatus.DISABLE.getStatus().toString());
            mobileEntity.put(UserInfoField.MOBILE, value);
            mobileEntity.put(UserInfoField.IS_BIND_MOBILE, StringUtils.isNoneBlank(value) ? EnableStatus.ENABLE.getStatus().toString()
                    : EnableStatus.DISABLE.getStatus().toString());
            result.put(uuid, mobileEntity);
        });
        return result;
    }

    private Map<String, String> findMobile(Collection<String> uuids) {
        SoaResponse response = client.call(host, "mpUser", "getMobileByUuids", uuids);
        // 需要设置重试次数
        if (response.fail()) {
            return Map.of();
        }

        return JsonUtils.stringToObject(response.getData(),new TypeReference<>() {});
    }

    public static String mobileMask(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        }
        if (mobile.length() < 9) {
            return mobile;
        }
        StringBuilder sb = new StringBuilder(mobile);
        sb.replace(3, 7, "****");
        return sb.toString();
    }
}
