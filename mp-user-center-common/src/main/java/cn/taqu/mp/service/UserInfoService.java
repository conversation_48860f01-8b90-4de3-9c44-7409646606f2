package cn.taqu.mp.service;

import cn.taqu.mp.component.FastInfoSearcher;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.constant.Gender;
import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.constant.UserInfoField;
import cn.taqu.mp.dto.MappingKey;
import cn.taqu.mp.dto.UserCoreInfoDto;
import cn.taqu.mp.util.AgeTool;
import cn.taqu.mp.util.AvatarTool;
import cn.taqu.mp.util.LogUtil;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RMapAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.StringCodec;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static cn.taqu.mp.constant.UserInfoConstant.*;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午1:29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public final class UserInfoService implements FastInfoSearcher<UserCoreInfoDto> {

    private final RedissonClient            redissonClient;

    private final StringRedisTemplate        redisTemplate;

    private final ObjectMapper                objectMapper;

    @Override
    public Map<String, ? extends Serializable> search(String myUuid, List<String> uuids) {
        return coreInfo(uuids, "0");
    }

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        return coreInfo(uuids, "0", fields);
    }

    @Override
    public Set<String> filterFields(Collection<String> fields) {
        Set<String> result = Sets.newHashSet(fields);
        var availableFields = MappingKeyScanner.me().fields(UserCoreInfoDto.class);
        // age -> 通过birth查询
        if (result.remove(UserInfoField.AGE)) {
            result.add(UserInfoField.BIRTH);
        }
        // 移除不符合范围的字段
        result.removeIf(f -> {
            if (availableFields.contains(f)) {
                return false;
            }
            return !UserInfoField.UUID.equals(f);
        });
        // 查询头像必须查询性别，兜底默认头像丢失情况
        if (result.contains(UserInfoField.AVATAR)) {
            result.add(UserInfoField.SEX_TYPE);
        }
        return result;
    }

    public Map<String, Map<String, String>> coreInfo(List<String> uuids, String version, Collection<String> fields) {
        var filteredFields = filterFields(fields);
        if (CollectionUtils.isEmpty(filteredFields)) {
            return new HashMap<>(2);
        }
        var infoMap = query(uuids, filteredFields);
        infoMap.forEach((uuid, info) -> {
            each(uuid, info, filteredFields, version);
            info.entrySet().removeIf(e -> !fields.contains(e.getKey()));
        });
        LogUtil.infoCtl("[coreInfo] result:{}", infoMap);
        return infoMap;
    }

    public Map<String, UserCoreInfoDto> coreInfo(List<String> uuids, String version) {
        var result = info(uuids, UserCoreInfoDto.class, () -> UserCoreInfoDto.UNKNOWN);
        result.forEach((uuid, dto) -> {
            dto.setUuid(uuid);
            String avatar = Optional.ofNullable(dto.getAvatar()).orElse(AvatarTool.DEFAULT_MALE_AVATAR);
            dto.setAvatar(AvatarTool.resolve(avatar, version));
            if (dto.getBirth() != null) {
                int age = AgeTool.calAgeFromBirth(dto.getBirth());
                dto.setAge(String.valueOf(age));
            }
        });
        LogUtil.infoCtl("[coreInfo] result:{}", JsonUtils.objectToString(result));
        return result;
    }

    @Override
    public void postSearch(Map<String, Map<String, String>> infoMap, Set<String> filteredFields) {
        infoMap.forEach((uuid, info) -> each(uuid, info, filteredFields, "0"));
    }

    private void each(String uuid, Map<String, String> info, Set<String> targetFields, String version) {
        // 默认值填充
        if (targetFields.contains(UserInfoField.AVATAR)) {
            info.put(UserInfoField.AVATAR, resolveAvatar(info, UserInfoField.AVATAR, version));
        }
        if (targetFields.contains(UserInfoField.AVATAR_ORIGIN)) {
            info.put(UserInfoField.AVATAR_ORIGIN, resolveAvatar(info, UserInfoField.AVATAR_ORIGIN, version));
        }
        if (targetFields.contains(UserInfoField.SEX_TYPE)) {
            String val = info.get(UserInfoField.SEX_TYPE);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.SEX_TYPE, DEFAULT_SEX_TYPE);
        }
        if (targetFields.contains(UserInfoField.ACCOUNT_NAME)) {
            String val = info.get(UserInfoField.ACCOUNT_NAME);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.ACCOUNT_NAME, DEFAULT_ACCOUNT_NAME);
        }
        if (targetFields.contains(UserInfoField.ACCOUNT_STATUS)) {
            String val = info.get(UserInfoField.ACCOUNT_STATUS);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.ACCOUNT_STATUS, DEFAULT_ACCOUNT_STATUS);
        }
        if (targetFields.contains(UserInfoField.ZHIMA_CERTIFICATION)) {
            String val = info.get(UserInfoField.ZHIMA_CERTIFICATION);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.ZHIMA_CERTIFICATION, DEFAULT_ZHI_MA_CERTIFICATION);
        }
        if (targetFields.contains(UserInfoField.REAL_PERSON_CERTIFICATION)) {
            String val = info.get(UserInfoField.REAL_PERSON_CERTIFICATION);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.REAL_PERSON_CERTIFICATION, DEFAULT_REAL_PERSON_CERTIFICATION);
        }
        if (targetFields.contains(UserInfoField.UUID)) {
            info.put(UserInfoField.UUID, uuid);
        }
        // 查年龄
        if (targetFields.contains(UserInfoField.BIRTH)) {
            String val = info.get(UserInfoField.BIRTH);
            if (StringUtils.isEmpty(val)) {
                info.put(UserInfoField.AGE, DEFAULT_AGE);
            } else {
                int age = AgeTool.calAgeFromBirth(Long.parseLong(val));
                info.put(UserInfoField.AGE, String.valueOf(age));
            }
        }
    }

    private String resolveAvatar(Map<String, String> info, String field, String version) {
        String avatar = info.get(field);
        if (StringUtils.isEmpty(avatar)) {
            // 查头像必须查性别，外面有手动处理
            String sexType = info.get(UserInfoField.SEX_TYPE);
            Integer gender = StringUtils.isNotBlank(sexType) ? Integer.parseInt(sexType) : Gender.MALE.val();
            avatar = AvatarTool.getDefAvatar(gender);
        }
        return AvatarTool.resolve(avatar, version);
    }

    @SuppressWarnings("all")
    public Map<String, Map<String, String>> query(List<String> uuids, Collection<String> fields) {
        var list = redisTemplate.executePipelined(new SessionCallback<Void>() {
            @Override
            public <K, V> Void execute(RedisOperations<K, V> operations) throws DataAccessException {
                for (String uuid : uuids) {
                    String key = RedisKeyConstant.INFOS.formatted(uuid);
                    RedisOperations<String, String> strOps = (RedisOperations<String, String>) operations;
                    strOps.<String, String>opsForHash().multiGet(key, fields);
                }
                return null;
            }
        }).stream().map(r -> {
            List<String> vl = (List<String>) r;
            Map<String, String> row = new HashMap<>(fields.size() + 1);
            int i = 0;
            for (String field : fields) {
                row.put(field, Optional.ofNullable(vl.get(i++)).orElse(""));
            }
            return row;
        }).collect(Collectors.toList());

        Map<String, Map<String, String>> result = new HashMap<>(list.size());
        for (int i = 0, len = list.size(); i < len; i++) {
            result.put(uuids.get(i), list.get(i));
        }
        return result;
    }

    @SuppressWarnings("all")
    public <T extends MappingKey> Map<String, T> info(List<String> uuids, Class<T> type, Supplier<T> def) {
        var targetFields = MappingKeyScanner.me().fields(type);
        RBatch pipeline = redissonClient.createBatch();
        Codec code = StringCodec.INSTANCE;
        Map<String, T> result = new HashMap<>(uuids.size());
        for (String uuid : uuids) {
            String key = RedisKeyConstant.INFOS.formatted(uuid);
            RMapAsync<String, String> map = pipeline.getMap(key, code);
            map.getAllAsync(targetFields).thenAccept(r -> {
                T t = null;
                try {
                    if (!r.isEmpty()) {
                        t = objectMapper.convertValue(r, type);
                    }
                } catch (Exception e) {
                    log.warn("Unable to parse info {} {}", uuid, r);
                }
                if (t == null && def != null) {
                    t = def.get();
                }
                result.put(uuid, t);
            });
        }
        pipeline.execute();
        return result;
    }

}
