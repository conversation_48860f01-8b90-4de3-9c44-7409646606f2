package cn.taqu.mp.service;

import cn.taqu.mp.constant.EnableStatus;
import cn.taqu.mp.dao.AccountLabelCfgDao;
import cn.taqu.mp.entity.AccountLabelCfg;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import reactor.core.scheduler.Schedulers;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 标签配置
 *
 * <AUTHOR>
 * @date 2025/5/6 10:27
 */
@Service
public class LabelCfgService implements InitializingBean {

    @Resource
    private AccountLabelCfgDao accountLabelCfgDao;

    public static Long lastTime;
    public static Map<Long, AccountLabelCfg> labelCfgMap;

    @Override
    public void afterPropertiesSet() throws Exception {
        lastTime = 0L;
        labelCfgMap = Maps.newHashMap();
        Schedulers.newSingle("LabelCfgScheduler", true).schedulePeriodically(this::refresh, 0, 5, TimeUnit.MINUTES);
    }

    /**
     * 刷新
     */
    public void refresh() {
        long updateTime = accountLabelCfgDao.findLastTime().getTime();
        if (lastTime > updateTime) {
            // 没更新
            return;
        }

        List<AccountLabelCfg> cfgList = accountLabelCfgDao.findByDataStatus(EnableStatus.ENABLE.getStatus());
        labelCfgMap = cfgList.stream().collect(Collectors.toMap(AccountLabelCfg::getId, Function.identity()));
        lastTime = updateTime + 1;
    }


}
