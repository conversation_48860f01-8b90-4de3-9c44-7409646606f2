package cn.taqu.mp.service;

import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.dao.AccountsLifeDao;
import cn.taqu.mp.dao.AccountsPhotoDao;
import cn.taqu.mp.dto.PhotoInfoDto;
import cn.taqu.mp.dto.UserAlbumInfoDto;
import cn.taqu.mp.entity.AccountsLife;
import cn.taqu.mp.entity.AccountsPhoto;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RBucketAsync;
import org.redisson.api.RMapAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 我的生活 + 相册照片查询
 * <AUTHOR>
 * @date 2025/4/27 下午1:58
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAlbumInfoService implements CategorizedInfoSearcher<UserAlbumInfoDto> {

    private final RedissonClient redissonBizClient;

    private final RedissonClient    redissonClient;

    private final AccountsLifeDao          lifeDao;

    private final AccountsPhotoDao        photoDao;

    /**
     * 1、我的生活缓存
     * 2、我的生活db
     * 3、我的生活排序
     * 4、相册缓存
     * 5、相册db
     */
    @Override
    public Map<String, UserAlbumInfoDto> search(String myUuid, List<String> uuids) {
        Codec codec = StringCodec.INSTANCE;
        Set<String> cacheMissed = new HashSet<>();
        RBatch pipeline = redissonBizClient.createBatch();
        Map<String, UserAlbumInfoDto> resultMap = new HashMap<>(uuids.size());
        for (String uuid : uuids) {
            String key = RedisKeyConstant.LIFE.formatted(uuid);
            RMapAsync<String, String> map = pipeline.getMap(key, codec);
            map.readAllMapAsync().thenAccept(valMap -> {
                if (valMap.isEmpty()) {
                    cacheMissed.add(uuid);
                } else {
                    List<UserAlbumInfoDto.LifeInfo> list = valMap.entrySet().stream().filter(e -> StringUtils.isNotBlank(e.getValue()))
                        .map(e -> {
                            UserAlbumInfoDto.LifeInfo row = JsonUtils.stringToObject(e.getValue(), UserAlbumInfoDto.LifeInfo.class);
                            row.setSeq(Integer.parseInt(e.getKey()));
                            return row;
                        }).filter(p -> AccountsLife.verifyVisitorStatus(p.getStatus())).collect(Collectors.toList());
                    UserAlbumInfoDto dto = new UserAlbumInfoDto(list);
                    resultMap.put(uuid, dto);
                }
            });
        }
        pipeline.execute();
        Set<String> hisUuids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(cacheMissed)) {
            List<PhotoInfoDto> list = lifeDao.listPassedLife(cacheMissed);
            Map<String, List<PhotoInfoDto>> lifeListMap = list.stream().collect(Collectors.groupingBy(PhotoInfoDto::getAccountUuid));
            for (String uuid : cacheMissed) {
                List<PhotoInfoDto> lifeList = lifeListMap.get(uuid);
                if (CollectionUtils.isNotEmpty(lifeList)) {
                    UserAlbumInfoDto dto = new UserAlbumInfoDto(JsonUtils.mapper().convertValue(lifeList, new TypeReference<>() {}));
                    resultMap.put(uuid, dto);
                } else {
                    hisUuids.add(uuid);
                }
            }
        }
        // 我的生活排序
        RBatch seqPipeline = redissonBizClient.createBatch();
        resultMap.forEach((uuid, dto) -> {
            String key = RedisKeyConstant.LIFE_SEQ.formatted(uuid);
            RBucketAsync<String> bucket = seqPipeline.getBucket(key, codec);
            bucket.getAsync().thenAccept(seq -> sort(dto.photoList(), seq));
        });
        seqPipeline.execute();
        // 相册查询
        if (CollectionUtils.isNotEmpty(hisUuids)) {
            resultMap.putAll(searchHisPhoto(hisUuids));
        }
        return resultMap;
    }

    private Map<String, UserAlbumInfoDto> searchHisPhoto(Collection<String> uuids) {
        // 旧版 查相册
        Map<String, UserAlbumInfoDto> resultMap = new HashMap<>(uuids.size());
        if (CollectionUtils.isNotEmpty(uuids)) {
            RBatch pipeline = redissonClient.createBatch();
            Set<String> cacheMissed = new HashSet<>();
            for (String uuid : uuids) {
                String key = RedisKeyConstant.HIS_PHOTO.formatted(uuid);
                RMapAsync<String, String> map = pipeline.getMap(key, StringCodec.INSTANCE);
                map.readAllMapAsync().thenAccept(valMap -> {
                    if (valMap.isEmpty()) {
                        cacheMissed.add(uuid);
                    } else {
                        List<UserAlbumInfoDto.PhotoInfo> list = valMap.entrySet().stream().filter(e -> StringUtils.isNotBlank(e.getValue()))
                            .map(e -> {
                                UserAlbumInfoDto.PhotoInfo row = JsonUtils.stringToObject(e.getValue(), UserAlbumInfoDto.PhotoInfo.class);
                                row.setSeq(Integer.parseInt(e.getKey()));
                                return row;
                            }).filter(p -> p.getSeq() > 1 && StringUtils.isNotBlank(p.getPhotoUrl()) && AccountsPhoto.verifyVisitorStatus(p.getStatus())).limit(5).collect(Collectors.toList());
                        UserAlbumInfoDto dto = new UserAlbumInfoDto(list);
                        resultMap.put(uuid, dto);
                    }
                });
            }
            pipeline.execute();

            if (CollectionUtils.isNotEmpty(cacheMissed)) {
                List<PhotoInfoDto> list = photoDao.listPassedPhoto(cacheMissed);
                Map<String, List<PhotoInfoDto>> photoListMap = list.stream().collect(Collectors.groupingBy(PhotoInfoDto::getAccountUuid));
                for (String uuid : cacheMissed) {
                    List<PhotoInfoDto> lifeList = photoListMap.get(uuid);
                    if (CollectionUtils.isNotEmpty(lifeList)) {
                        List<UserAlbumInfoDto.PhotoInfo> photoList = JsonUtils.mapper().convertValue(lifeList, new TypeReference<>() {});
                        photoList = photoList.stream()
                            .filter(p -> p.getSeq() > 1 && StringUtils.isNotBlank(p.getPhotoUrl()))
                            .limit(5)
                            .collect(Collectors.toList());
                        UserAlbumInfoDto dto = new UserAlbumInfoDto(photoList);
                        resultMap.put(uuid, dto);
                    } else {
                        log.info("未查询到图片信息 {}", uuid);
                        resultMap.put(uuid, UserAlbumInfoDto.UNKNOWN);
                    }
                }
            }
        }
        return resultMap;
    }

    private void sort(List<? extends UserAlbumInfoDto.PhotoInfo> photoList, String seq) {
        // 我的生活排序
        if (CollectionUtils.isEmpty(photoList)) {
            return;
        }
        if (StringUtils.isBlank(seq)) {
            return;
        }
        List<Integer> seqList = Stream.of(seq.split(",")).map(Integer::parseInt).toList();
        // 2,3,5,4,1 -> 2, 30, 500, 4000, 10000
        Map<Integer, Integer> priorityMap = new HashMap<>(seqList.size());
        for (int i = 0, length = seqList.size(); i < length; i++) {
            Integer s = seqList.get(i);
            int f = (int) Math.pow(10, i);
            priorityMap.put(s, s * f);
        }
        photoList.sort(Comparator.comparing(a -> priorityMap.get(a.getSeq())));
    }

}
