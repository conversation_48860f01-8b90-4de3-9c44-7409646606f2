package cn.taqu.mp.service;

import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.dao.AccountsMajorDao;
import cn.taqu.mp.dao.SchoolDao;
import cn.taqu.mp.dto.SchoolMajorDto;
import cn.taqu.mp.dto.UserSchoolInfoDto;
import cn.taqu.mp.entity.AccountsMajor;
import cn.taqu.mp.entity.School;
import cn.taqu.mp.vo.UserSchoolInfoVo;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RBucketAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学校专业
 * <AUTHOR>
 * @date 2025/4/28 下午3:10
 */
@Service
@RequiredArgsConstructor
public class UserSchoolInfoService implements CategorizedInfoSearcher<UserSchoolInfoDto> {

    private final Codec                      codec = new TypedJsonJacksonCodec(SchoolMajorDto.class);

    private final RedissonClient redissonBizClient;

    private final AccountsMajorDao        majorDao;

    private final SchoolDao              schoolDao;

    @Override
    public Map<String, UserSchoolInfoVo> search(String myUuid, List<String> uuids) {
        Map<String, UserSchoolInfoDto> schoolMap = new HashMap<>(uuids.size());
        RBatch pipeline = redissonBizClient.createBatch();
        Set<String> cacheMissed = new HashSet<>();
        for (String uuid : uuids) {
            String key = RedisKeyConstant.SCHOOL_MAJOR.formatted(uuid);
            RBucketAsync<SchoolMajorDto> bucket = pipeline.getBucket(key, codec);
            bucket.getAsync().thenAccept(d -> {
                if (d != null) {
                    UserSchoolInfoDto userSchool = new UserSchoolInfoDto();
                    userSchool.setMajor(d.getMajor());
                    userSchool.setSchoolId(d.getSchoolId());
                    schoolMap.put(uuid, userSchool);
                } else {
                    // 查库
                    cacheMissed.add(uuid);
                    schoolMap.put(uuid, UserSchoolInfoDto.UNKNOWN);
                }
            });
        }
        pipeline.execute();
        // 未命中缓存查db
        if (CollectionUtils.isNotEmpty(cacheMissed)) {
            List<AccountsMajor> majorList = majorDao.findByAccountUuidIn(uuids);
            for (var major : majorList) {
                UserSchoolInfoDto userSchool = new UserSchoolInfoDto();
                userSchool.setSchoolId(major.getSchoolId());
                userSchool.setMajor(major.getMajor());
                schoolMap.put(major.getAccountUuid(), userSchool);
            }
        }
        // db填充学校名字
        Set<Long> schoolIds = schoolMap.values().stream().map(UserSchoolInfoDto::getSchoolId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(schoolIds)) {
            var list = schoolDao.findByIdIn(schoolIds);
            Map<Long, String> nameMap = list.stream().collect(Collectors.toMap(School::getId, School::getSchoolName));
            schoolMap.values().forEach(r -> {
                if (r.getSchoolId() != null) {
                    r.setSchoolName(nameMap.get(r.getSchoolId()));
                }
            });
        }
        return JsonUtils.mapper().convertValue(schoolMap, new TypeReference<>() {});
    }

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        if (fields.contains("school_name") || fields.contains("major")) {
            var map = search("", uuids);
            return JsonUtils.mapper().convertValue(map, new TypeReference<>() {});
        }
        return new HashMap<>(2);
    }
}
