package cn.taqu.mp.service;

import cn.hutool.core.util.NumberUtil;
import cn.taqu.mp.component.FastInfoSearcher;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.constant.*;
import cn.taqu.mp.dao.AccountLabelCfgDao;
import cn.taqu.mp.dao.AccountLabelDao;
import cn.taqu.mp.dao.AccountsCertificationDao;
import cn.taqu.mp.dto.UserCardInfoDto;
import cn.taqu.mp.entity.AccountLabel;
import cn.taqu.mp.entity.AccountLabelCfg;
import cn.taqu.mp.entity.Region;
import cn.taqu.mp.soa.EncryptClient;
import cn.taqu.mp.util.AgeTool;
import cn.taqu.mp.util.AvatarTool;
import cn.taqu.mp.util.DateUtil;
import cn.taqu.mp.util.LocationUtils;
import cn.taqu.mp.vo.UserCardInfoVo;
import cn.taqu.soa.utils.JsonUtils;
import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RBucketAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础卡片信息
 * <AUTHOR>
 * @date 2025/4/27 下午1:58
 */
@Slf4j
@Service
public class UserCardInfoService implements FastInfoSearcher<UserCardInfoDto> {

    @Resource
    private UserInfoService infoService;
    @Resource
    private RedissonClient redissonBizClient;
    @Resource
    private AccountsCertificationDao accountsCertificationDao;
    @Resource
    private EncryptClient encryptClient;
    @Resource
    private AccountLabelCfgDao accountLabelCfgDao;
    @Resource
    private AccountLabelDao accountLabelDao;
    @Resource
    private RegionService regionService;

    @Override
    public Map<String, UserCardInfoVo> search(String myUuid, List<String> uuids) {
        uuids = new ArrayList<>(uuids);
        uuids.addFirst(myUuid);
        var infoMap = infoService.info(uuids, UserCardInfoDto.class, () -> UserCardInfoDto.UNKNOWN);
        UserCardInfoDto myInfo = infoMap.get(myUuid);
        infoMap.forEach((uuid, target) -> {
            target.setUuid(uuid);
            if (StringUtils.isEmpty(target.getAvatar())) {
                target.setAvatar(StringUtils.isNotBlank(target.getSexType()) ? AvatarTool.getDefAvatar(Integer.parseInt(target.getSexType())) : AvatarTool.getDefAvatar(Gender.MALE.val()));
            } else {
                target.setAvatar(AvatarTool.resolve(target.getAvatar()));
            }
            if (target.getBirth() != null) {
                int age = AgeTool.calAgeFromBirth(target.getBirth());
                target.setAge(String.valueOf(age));
            } else {
                target.setAge(UserInfoConstant.DEFAULT_AGE);
            }
            // 距离处理
            distanceProcess(myInfo, target);
        });
        infoMap.remove(myUuid);

        // 标签获取
        labelProcess(infoMap, myUuid, Integer.parseInt(myInfo.getSexType()));

        Map<String, UserCardInfoVo> result = new HashMap<>(infoMap.size());
        infoMap.forEach((uuid, dto) -> {
            // dto转vo字段处理
            UserCardInfoVo vo = JsonUtils.mapper().convertValue(dto, UserCardInfoVo.class);
            vo.setVoiceSignInfo(new UserCardInfoVo.VoiceSignInfo(dto.getPassVoiceSignUrl(), dto.getPassVoiceSignDuration()));
            vo.setLabels(new UserCardInfoVo.Label(dto.getSameCategory(), dto.getLabelList()));
            vo.setPersonalProfile(dto.getPassPersonalProfile());

            vo.setIntroduction(new UserCardInfoVo.Introduction(dto.getPassIntroductionContent(), dto.getPassIntroductionImgs()));
            result.put(uuid, vo);
        });
        return result;
    }

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        var targetFields = filterFields(fields);
        if (targetFields.isEmpty()) {
            return new HashMap<>(2);
        }
        Map<String, Map<String, String>> result = infoService.query(uuids, targetFields);
        postSearch(result, targetFields);
        return result;
    }

    @Override
    public Set<String> filterFields(Collection<String> fields) {
        var availableFields = MappingKeyScanner.me().fields(UserCardInfoDto.class);
        Set<String> result = new HashSet<>(fields);
        if (fields.contains(UserInfoField.AGE)) {
            result.add(UserInfoField.BIRTH);
        }
        if (fields.contains(UserInfoField.CONSTELLATION)) {
            result.add(UserInfoField.BIRTH);
        }
        // 移除不符合范围的字段
        result.removeIf(f -> {
            if (availableFields.contains(f)) {
                return false;
            }
            return !UserInfoField.UUID.equals(f);
        });
        // 查头像必须查性别
        if (result.contains(UserInfoField.AVATAR)) {
            result.add(UserInfoField.SEX_TYPE);
        }
        if (result.contains(UserInfoField.BASEADDR_CITY)) {
            result.add(UserInfoField.BASEADDR);
        }
        return result;
    }

    @Override
    public void postSearch(Map<String, Map<String, String>> infoMap, Set<String> filteredFields) {
        infoMap.forEach((uuid, info) -> {
            if (filteredFields.contains(UserInfoField.UUID)) {
                info.put(UserInfoField.UUID, uuid);
            }
            // 头像兜底
            if (filteredFields.contains(UserInfoField.AVATAR) && StringUtils.isBlank(info.get(UserInfoField.AVATAR))) {
                String sexType = info.get(UserInfoField.SEX_TYPE);
                Integer gender = StringUtils.isBlank(sexType) ? Gender.MALE.val() : Integer.parseInt(sexType);
                String avatar = AvatarTool.resolve(AvatarTool.getDefAvatar(gender));
                info.put(UserInfoField.AVATAR, avatar);
            }
            // 年龄特殊处理
            if (filteredFields.contains(UserInfoField.AGE)) {
                String birth = info.get(UserInfoField.BIRTH);
                int age = StringUtils.isNotBlank(birth) ?
                    AgeTool.calAgeFromBirth(Long.parseLong(birth)) : 0;
                info.put(UserInfoField.AGE, String.valueOf(age));
            }
            // 星座处理
            if (filteredFields.contains(UserInfoField.CONSTELLATION)) {
                String birth = info.get(UserInfoField.BIRTH);
                String constellation = StringUtils.isNotBlank(birth) ?
                    DateUtil.getConstellationFromSeconds(Long.parseLong(birth)) : "";
                info.put(UserInfoField.CONSTELLATION, constellation);
            }
        });
        if (filteredFields.contains(UserInfoField.BASEADDR_CITY)) {
            Set<Long> cityIdSet = new HashSet<>(8);
            Map<String, Long> userCityIdMap = new HashMap<>(infoMap.size());
            for (var entry : infoMap.entrySet()) {
                String uuid = entry.getKey();
                var info = entry.getValue();
                String baseAddress = info.get(UserInfoField.BASEADDR);
                if (StringUtils.isNotBlank(baseAddress)) {
                    String[] valueArray = StringUtils.split(baseAddress, ",");
                    String cityId = ArrayUtils.getLength(valueArray) > 1 ? StringUtils.trimToEmpty(valueArray[1]) : "";
                    Long cid = NumberUtil.isNumber(cityId) ? Long.valueOf(cityId) : null;
                    Optional.ofNullable(cid).ifPresent(id -> {
                        cityIdSet.add(cid);
                        userCityIdMap.put(uuid, cid);
                    });
                }
            }
            var regionMap = regionService.findRegion(cityIdSet);
            infoMap.forEach((uuid, info) -> {
                var cityId = userCityIdMap.get(uuid);
                if (cityId != null) {
                    info.put(UserInfoField.BASEADDR_CITY, Optional.ofNullable(regionMap.get(cityId)).map(Region::getRegion_name).orElse(StringPool.EMPTY));
                } else {
                    info.put(UserInfoField.BASEADDR_CITY, StringPool.EMPTY);
                }
            });
        }
    }

    /**
     * 标签获取
     *
     * @param infoMap
     */
    private void labelProcess(Map<String, UserCardInfoDto> infoMap, String myUuid, Integer myGender) {
        Map<String, String> labelIdMap = findLabelIdMap(infoMap, myUuid);
        if (labelIdMap.isEmpty()) {
            return;
        }

        // 获取我的分类
        Set<Long> myCategory = getMyCategory(labelIdMap.get(myUuid), myGender);
        labelIdMap.remove(myUuid);

        labelIdMap.forEach((uuid, labelStr) -> {
            String[] split = labelStr.split(",");
            if (split.length == 0) {
                return;
            }
            Set<Long> sameCategory = Sets.newHashSet();
            List<UserCardInfoDto.Label> labels = Lists.newArrayList();

            for (String labelId : split) {
                AccountLabelCfg labelCfg = LabelCfgService.labelCfgMap.get(Long.parseLong(labelId));
                if (labelCfg == null) {
                    log.warn("标签配置不存在:{},uuid:{}", labelId, uuid);
                    continue;
                }

                // 标签校验
                if (!validGender(labelCfg, Integer.parseInt(infoMap.get(uuid).getSexType()))) {
                    continue;
                }

                // 是否相似分类
                boolean isSame = CollectionUtils.isNotEmpty(myCategory) && myCategory.contains(labelCfg.getParentId());
                if (isSame) {
                    sameCategory.add(labelCfg.getParentId());
                }

                // 标签内容
                UserCardInfoDto.Label label = new UserCardInfoDto.Label();
                label.setLabelName(labelCfg.getContent());
                label.setIsHighlight(isSame ? 1 : 0);
                if (isSame) {
                    labels.addFirst(label);
                } else {
                    labels.add(label);
                }
            }

            // 相同分类名称处理
            List<String> sameCategoryContent = sameCategory.stream().map(category -> {
                AccountLabelCfg labelCfg = LabelCfgService.labelCfgMap.get(category);
                return labelCfg.getContent();
            }).toList();

            UserCardInfoDto cardInfo = infoMap.get(uuid);
            cardInfo.setLabelList(labels);
            cardInfo.setSameCategory(sameCategoryContent);
        });
    }

    /**
     * 获取我的分类
     *
     * @param labelStr
     * @return
     */
    private Set<Long> getMyCategory(String labelStr, Integer myGender) {
        Set<Long> myCategory = Sets.newHashSet();
        if (StringUtils.isBlank(labelStr)) {
            return myCategory;
        }

        String[] split = labelStr.split(",");

        for (String labelId : split) {
            AccountLabelCfg labelCfg = LabelCfgService.labelCfgMap.get(Long.parseLong(labelId));
            if (labelCfg == null) {
                log.warn("标签配置不存在:{}", labelId);
                continue;
            }

            if (!validGender(labelCfg, myGender)) {
                continue;
            }

            myCategory.add(labelCfg.getParentId());
        }
        return myCategory;
    }

    /**
     * 获取用户标签id
     *
     * @param infoMap
     * @param myUuid
     * @return
     */
    private Map<String, String> findLabelIdMap(Map<String, UserCardInfoDto> infoMap, String myUuid) {
        HashMap<String, String> labelIdMap = Maps.newHashMap();

        List<String> uuids = Lists.newArrayList(infoMap.keySet());
        uuids.add(myUuid);
        RBatch pipeline = redissonBizClient.createBatch();
        StringCodec codec = StringCodec.INSTANCE;
        List<String> needDb = Lists.newArrayList();
        for (String uuid : uuids) {
            String key = String.format(RedisKeyConstant.LABEL, uuid);
            RBucketAsync<String> bucket = pipeline.getBucket(key, codec);
            bucket.getAsync().thenAccept(label -> {
                if (StringUtils.isNotBlank(label)) {
                    labelIdMap.put(uuid, label);
                } else {
                    needDb.add(uuid);
                }
            });
        }
        pipeline.execute();

        // 这里需要查db
        if (CollectionUtils.isNotEmpty(needDb)) {
            List<AccountLabel> labels = accountLabelDao.findByAccountUuidIn(needDb);
            if (CollectionUtils.isNotEmpty(labels)) {
                Map<String, String> map = labels.stream().collect(Collectors.toMap(AccountLabel::getAccountUuid, AccountLabel::getLabelIds));
                labelIdMap.putAll(map);
                RBatch pipeline2 = redissonBizClient.createBatch();
                for (AccountLabel label : labels) {
                    String key = String.format(RedisKeyConstant.LABEL, label.getAccountUuid());
                    RBucketAsync<String> bucket = pipeline2.getBucket(key, codec);
                    bucket.setAsync(label.getLabelIds(), Duration.ofDays(7));
                }
                pipeline2.execute();
            }
        }

        return labelIdMap;
    }

    /**
     * 标签性别校验
     */
    private boolean validGender(AccountLabelCfg labelCfg, Integer gender) {
        // 通用配置
        if (Gender.UNKNOWN.match(labelCfg.getGender())) {
            return true;
        }

        return Objects.equals(labelCfg.getGender(), gender);
    }

    /**
     * 距离处理
     *
     * @param myInfo
     * @param target
     */
    private void distanceProcess(UserCardInfoDto myInfo, UserCardInfoDto target) {
        if (!EnableStatus.ENABLE.match(myInfo.getEnableLocation())
            || !EnableStatus.ENABLE.match(target.getEnableLocation())) {
            return;
        }

        Double d = LocationUtils.getDistance(myInfo.getLatitude(), myInfo.getLongitude(), target.getLatitude(), target.getLongitude());
        if (d == null) {
            return;
        }

        BigDecimal b = BigDecimal.valueOf(d).setScale(2, RoundingMode.HALF_UP);
        String disStr = String.valueOf(b);
        if ("0.00".equals(disStr)) {
            disStr = "0.01";
        }
        target.setDistance(disStr);
        target.setDistanceUnit("km");
    }

}
