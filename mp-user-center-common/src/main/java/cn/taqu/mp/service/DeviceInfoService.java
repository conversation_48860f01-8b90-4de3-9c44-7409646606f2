package cn.taqu.mp.service;

import cn.taqu.mp.dao.MemberDao;
import cn.taqu.mp.dto.MemberDto;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/4 下午3:56
 */
@Service
@RequiredArgsConstructor
public final class DeviceInfoService {

    private final MemberDao   memberDao;

    @Resource
    private UserInfoService infoService;

    public Map<String, Long> memberIds(List<String> uuids) {
        String memberIdKey = "member_id";
        Map<String, Map<String, String>> memberIdMap = infoService.query(uuids, List.of(memberIdKey));
        return memberIdMap.entrySet().stream().filter(e -> StringUtils.hasText(e.getValue().get(memberIdKey))).collect(Collectors.toMap(Map.Entry::getKey, e -> Long.parseLong(e.getValue().get(memberIdKey))));
    }

    public Map<String, String> tokenInfo(List<String> uuids) {
        var memberIdMap = memberIds(uuids);
        Collection<Long> memberIds = memberIdMap.values();
        var members = memberDao.findByIdIn(memberIds);
        if (members.isEmpty()) {
            return Map.of();
        }
        var tokenMap = members.stream().collect(Collectors.toMap(MemberDto::id, MemberDto::token));
        Map<String, String> result = new HashMap<>(uuids.size());
        for (var entry : memberIdMap.entrySet()) {
            String token = tokenMap.get(entry.getValue());
            if (token != null) {
                result.put(entry.getKey(), token);
            }
        }
        return result;
    }
}
