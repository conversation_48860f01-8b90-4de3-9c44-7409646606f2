package cn.taqu.mp.service;

import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.dao.RegionDao;
import cn.taqu.mp.entity.Region;
import cn.taqu.soa.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RMapAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 地区服务
 * 提供地区信息查询的公共服务
 * 
 * <AUTHOR>
 * @date 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RegionService {

    private final RegionDao regionDao;
    private final RedissonClient redissonClient;

    /**
     * 获取地区信息（从缓存和数据库）
     * @param ids 城市ID集合
     * @return 地区信息映射
     */
    public Map<Long, Region> findRegion(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }

        Map<Long, Region> result = new HashMap<>();
        Set<Long> cacheMissed = new HashSet<>();

        RBatch pipeline = redissonClient.createBatch();
        Codec code = StringCodec.INSTANCE;
        ids.forEach(id -> {
            String key = String.format(RedisKeyConstant.REGION, id);
            RMapAsync<String, String> map = pipeline.getMap(key, code);
            map.readAllMapAsync().thenAccept(r -> {
                if (r == null || r.isEmpty()) {
                    cacheMissed.add(id);
                    return;
                }
                //缓存命中
                Region region = JsonUtils.mapper().convertValue(r, Region.class);
                result.put(id, region);
            });
        });
        pipeline.execute();

        // 缓存未命中的查询数据库
        if (CollectionUtils.isNotEmpty(cacheMissed)) {
            List<Region> regionList = regionDao.findByIdIn(cacheMissed);
            regionList.forEach(r -> result.put(r.getId(), r));
        }
        return result;
    }

}
