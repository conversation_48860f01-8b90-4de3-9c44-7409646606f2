package cn.taqu.mp.service;

import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.component.FastInfoSearcher;
import cn.taqu.mp.dto.UserSkeletonInfoDto;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;

/**
 * core + card + certification + mobile
 * <AUTHOR>
 * @date 2025/7/28 下午9:24
 */
@Service
@RequiredArgsConstructor
public class UserSkeletonInfoService implements CategorizedInfoSearcher<UserSkeletonInfoDto> {

    @Resource
    private UserInfoService infoService;

    private final List<FastInfoSearcher<?>> searchers;

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        Map<CategorizedInfoSearcher<?>, Set<String>> filteredFieldMap = new HashMap<>(4);
        Set<String> fieldSet = new HashSet<>(32);
        for (var searcher : searchers) {
            var fs = searcher.filterFields(fields);
            filteredFieldMap.put(searcher, fs);
            fieldSet.addAll(fs);
        }

        var infoMap = infoService.query(uuids, fieldSet);
        searchers.forEach(s -> s.postSearch(infoMap, filteredFieldMap.get(s)));
        // 移除不需要字段
        infoMap.values().forEach(m -> m.entrySet().removeIf(e -> !fields.contains(e.getKey())));
        return infoMap;
    }

    @Override
    public Map<String, ? extends Serializable> search(String myUuid, List<String> uuids) {
        throw new UnsupportedOperationException("Not yet released");
    }
}
