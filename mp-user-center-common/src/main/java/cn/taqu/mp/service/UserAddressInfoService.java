package cn.taqu.mp.service;

import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.dto.UserAddressInfoDto;
import cn.taqu.mp.entity.Region;
import cn.taqu.mp.vo.UserAddressInfoVo;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户地址信息服务
 *
 * <AUTHOR>
 * @date 2025/7/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAddressInfoService implements CategorizedInfoSearcher<UserAddressInfoDto> {

    private final RegionService regionService;
    private final UserInfoService userInfoService;

    @Override
    public Map<String, UserAddressInfoVo> search(String myUuid, List<String> uuids) {
        Map<String, UserAddressInfoDto> addressDtoMap = userInfoService.info(uuids, UserAddressInfoDto.class, () -> UserAddressInfoDto.UNKNOWN);
        return processAddressInfo(addressDtoMap);
    }

    /**
     * 处理地址信息
     */
    private Map<String, UserAddressInfoVo> processAddressInfo(Map<String, UserAddressInfoDto> addressDtoMap) {
        Map<String, UserAddressInfoVo> result = new HashMap<>();
        Set<Long> cityIdSet = new HashSet<>();
        Map<String, Long> uuidCityMap = new HashMap<>();

        for (Map.Entry<String, UserAddressInfoDto> entry : addressDtoMap.entrySet()) {
            String accountUuid = entry.getKey();
            UserAddressInfoDto dto = entry.getValue();
            
            UserAddressInfoVo addressVo = new UserAddressInfoVo();
            result.put(accountUuid, addressVo);
            
            if (dto != null && StringUtils.isNotEmpty(dto.getBaseaddr())) {
                String[] addressArray = StringUtils.split(dto.getBaseaddr(), StringPool.COMMA);
                String cityId = ArrayUtils.getLength(addressArray) > 1 ? StringUtils.trimToEmpty(addressArray[1]) : StringPool.EMPTY;
                if (StringUtils.isNumeric(cityId)) {
                    Long cityIdLong = Long.valueOf(cityId);
                    cityIdSet.add(cityIdLong);
                    uuidCityMap.put(accountUuid, cityIdLong);
                    addressVo.setCity(cityIdLong);
                }
            }
        }

        // 查询城市和省份信息
        if (!cityIdSet.isEmpty()) {
            Map<Long, Region> cityRegionMap = regionService.findRegion(cityIdSet);
            Set<Long> provinceIdSet = cityRegionMap.values().stream()
                    .map(Region::getParent_id)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            Map<Long, Region> provinceRegionMap = regionService.findRegion(provinceIdSet);

            // 填充地址信息
            uuidCityMap.forEach((uuid, cityId) -> {
                UserAddressInfoVo addressVo = result.get(uuid);
                Region city = cityRegionMap.get(cityId);
                if (city != null) {
                    addressVo.setCityName(city.getRegion_name());
                    addressVo.setLongitude(StringUtils.trimToEmpty(city.getLongitude()));
                    addressVo.setLatitude(StringUtils.trimToEmpty(city.getLatitude()));
                    
                    if (city.getParent_id() != null) {
                        addressVo.setProvince(city.getParent_id());
                        Region province = provinceRegionMap.get(city.getParent_id());
                        if (province != null) {
                            addressVo.setProvinceName(province.getRegion_name());
                        }
                    }
                }
            });
        }

        return result;
    }
}
