package cn.taqu.mp.service;

import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.constant.UserInfoField;
import cn.taqu.mp.dto.UserLocationInfoDto;
import cn.taqu.mp.entity.Region;
import cn.taqu.mp.vo.UserLocationInfoVo;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RMapAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 位置信息
 * <AUTHOR>
 * @date 2025/4/18 上午11:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public final class UserLocationInfoService implements CategorizedInfoSearcher<UserLocationInfoDto> {

    private final RedissonClient       redissonClient;

    private final RegionService         regionService;

    @Override
    public Map<String, UserLocationInfoVo> search(String myUuid, List<String> uuids) {
        var targetFields = MappingKeyScanner.me().fields(UserLocationInfoDto.class);
        return doSearch(targetFields, uuids);
    }

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        var availableFields = MappingKeyScanner.me().fields(UserLocationInfoDto.class);
        Set<String> targetFields = new HashSet<>(fields);
        // 移除不符合范围的字段
        targetFields.removeIf(r -> !availableFields.contains(r));
        if (fields.contains(UserInfoField.CITY_ID)
                || fields.contains(UserInfoField.IP_PRO)
                || fields.contains(UserInfoField.IP_CITY)) {
            targetFields.add(UserInfoField.LOCATION_CITY_ID);
        }
        if (targetFields.isEmpty()) {
            return new HashMap<>(2);
        }
        var map = doSearch(targetFields, uuids);
        return JsonUtils.mapper().convertValue(map, new TypeReference<>() {});
    }

    private Map<String, UserLocationInfoVo> doSearch(Set<String> targetFields, List<String> uuids) {
        RBatch pipeline = redissonClient.createBatch();
        Map<String, UserLocationInfoVo> result = new HashMap<>(uuids.size());
        for (String uuid : uuids) {
            String key = RedisKeyConstant.LOCATION.formatted(uuid);
            RMapAsync<String, String> map = pipeline.getMap(key, StringCodec.INSTANCE);
            map.getAllAsync(targetFields).thenAccept(r -> {
                UserLocationInfoVo vo = UserLocationInfoVo.UNKNOWN;
                try {
                    if (!r.isEmpty()) {
                        UserLocationInfoDto dto = JsonUtils.mapper().convertValue(r, UserLocationInfoDto.class);
                        vo = new UserLocationInfoVo();
                        vo.setLatitude(dto.getLatitude());
                        vo.setLongitude(dto.getLongitude());
                        vo.setCityId(dto.getCityId());
                    } else {
                        vo.setLatitude(0.0);
                        vo.setLongitude(0.0);
                        vo.setCityId(0L);
                        log.warn("定位数据丢失:{}", uuid);
                    }
                } catch (Exception e) {
                    log.warn("Unable to parse location {} {}", uuid, r);
                }
                result.put(uuid, vo);
            });
        }
        pipeline.execute();
        cityProcess(result);
        return result;
    }

    /**
     * 城市名称后置处理。
     *
     * @param result
     */
    private void cityProcess(Map<String, UserLocationInfoVo> result) {
        // 取cityId
        Set<Long> cityIdSet = result.values().stream().map(UserLocationInfoVo::getCityId).filter(k -> Objects.nonNull(k) && k != 0).collect(Collectors.toSet());
        // 查城市名称 && 赋值
        Map<Long, Region> cityMap = regionService.findRegion(cityIdSet);
        Map<Long, Region> provinceMap = regionService.findRegion(cityMap.values().stream().map(Region::getParent_id).collect(Collectors.toSet()));
        result.values().forEach(location -> {
            Long cityId = location.getCityId();
            if (cityId != null && cityId != 0) {
                Region city = cityMap.get(cityId);
                location.setIpCity(city.getRegion_name().replace("市", ""));
                if (city.getParent_id() != null && provinceMap.containsKey(city.getParent_id())) {
                    location.setIpPro(provinceMap.get(city.getParent_id()).getRegion_name().replace("省", ""));
                }
            } else {
                location.setIpCity("");
                location.setIpPro("");
            }
        });
    }

}
