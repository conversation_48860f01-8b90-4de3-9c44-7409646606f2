FROM taqu-onlinie-registry-registry-vpc.cn-zhangjiakou.cr.aliyuncs.com/taqu/openjdk:centos7-21.0.2

# 项目名称, 请自行替换
ENV APP_NAME=mp-user-center

# 根据项目修改打包
COPY mp-user-center-server/target/mp-user-center-server.jar /data/html/${APP_NAME}.jar

# java 启动参数, 请谨慎变更
CMD java -server -Xmx4g -Xms4g \
    -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m \
    -XX:+UnlockDiagnosticVMOptions -XX:NativeMemoryTracking=detail \
    -XX:MinMetaspaceFreeRatio=50 -XX:MaxMetaspaceFreeRatio=80 \
    -XX:+UseZGC -XX:+ZGenerational -XX:CICompilerCount=3\
    -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/${POD_NAME}-oom-%p-%t.hprof \
    -Xlog:gc*,gc+heap=debug:file=/data/logs/${POD_NAME}-gc-%p-%t.log:time,uptime,level,tags \
    -Djava.security.egd=file:/dev/urandom \
    -jar /data/html/${APP_NAME}.jar \
    --server.port=80 --spring.profiles.active=prod --cli --server.address=0.0.0.0
