package cn.taqu.mp.api;

import cn.taqu.core.bean.JsonResult;
import cn.taqu.mp.service.DeviceInfoService;
import cn.taqu.soa.web.annotation.RpcAnnotation;
import cn.taqu.soa.web.protocol.http.RequestParams;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午1:44
 */
@RpcAnnotation
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api", params = "service=device")
public class DeviceInfoController {

    private final DeviceInfoService deviceService;

    /**
     * 设备信息查询
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getTokenByUuid")
    public JsonResult info(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        return JsonResult.success(deviceService.tokenInfo(List.of(uuids)));
    }


}
