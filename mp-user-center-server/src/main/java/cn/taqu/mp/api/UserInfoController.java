package cn.taqu.mp.api;

import cn.taqu.core.bean.JsonResult;
import cn.taqu.mp.component.UserInfoAggregator;
import cn.taqu.mp.constant.UserInfoCategory;
import cn.taqu.mp.service.UserAddressInfoService;
import cn.taqu.mp.service.UserInfoService;
import cn.taqu.mp.service.UserLocationInfoService;
import cn.taqu.mp.vo.UserAddressInfoVo;
import cn.taqu.soa.web.annotation.RpcAnnotation;
import cn.taqu.soa.web.protocol.http.RequestParams;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/15 下午1:44
 */
@RpcAnnotation
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/api", params = "service=info")
public class UserInfoController {

    private final UserInfoService                    infoService;

    private final UserLocationInfoService    locationInfoService;

    private final UserInfoAggregator              infoAggregator;

    private final UserAddressInfoService      addressInfoService;

    @RequestMapping(params = "method=getInfoByUuid")
    public JsonResult getInfoByUuid(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        String[] fields = params.getFormStringArrayOption(1);
        String version = params.getFormStringDefault(2, "0");

        return ArrayUtils.isEmpty(fields) ?
            JsonResult.success(infoService.coreInfo(List.of(uuids), version)) :
            JsonResult.success(infoService.coreInfo(List.of(uuids), version, Sets.newHashSet(fields))
        );
    }

    @RequestMapping(params = "method=getLocationByUuid")
    public JsonResult location(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        return JsonResult.success(locationInfoService.search("", List.of(uuids)));
    }

    /**
     * 详细基础信息
     *
     * @param params
     * @return
     */
    @RequestMapping(params = "method=getFullDetailInfo")
    public JsonResult getFullDetailInfo(RequestParams params) {
        String[] uuids = params.getFormStringArray(0);
        String[] fields = params.getFormStringArray(1);
        return JsonResult.success(
            infoAggregator.flatAggregate(
                List.of(uuids),
                List.of(UserInfoCategory.SKELETON,
                        UserInfoCategory.LOCATION,
                        UserInfoCategory.IDEAL_TARGET,
                        UserInfoCategory.SCHOOL
                ),
                Sets.newHashSet(fields)
            )
        );
    }

    @RequestMapping(params = "method=getDetailByUuid")
    public JsonResult getDetailByUuid(RequestParams params) {
        String myUuid = params.getFormString(0);
        String[] uuids = params.getFormStringArray(1);
        return JsonResult.success(
            infoAggregator.flatAggregate(
                myUuid, List.of(uuids),
                UserInfoCategory.CARD, UserInfoCategory.ALBUM,
                UserInfoCategory.SCHOOL, UserInfoCategory.IDEAL_TARGET,
                UserInfoCategory.LOCATION, UserInfoCategory.CERTIFICATION
            )
        );
    }

    /**
     * 用户地址信息
     * @param params 请求参数
     * @return 用户地址信息
     */
    @RequestMapping(params = "method=getAddressByUuid")
    public JsonResult getAddressByUuid(RequestParams params) {
        String[] accountUuids = params.getFormStringArray(0);
        Map<String, UserAddressInfoVo> map = addressInfoService.search("", List.of(accountUuids));
        return JsonResult.success(map);
    }

}
