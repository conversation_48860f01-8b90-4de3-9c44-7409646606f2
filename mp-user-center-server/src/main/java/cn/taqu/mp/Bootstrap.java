package cn.taqu.mp;

import cn.taqu.mp.plugin.NacosConfigPlugin;
import cn.taqu.soa.log.EnableAccessLogger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2025/1/14 下午4:09
 */
@EnableAccessLogger
@ComponentScan("cn.taqu")
@Import(NacosConfigPlugin.class)
@SpringBootApplication
public class Bootstrap {

    public static void main(String[] args) {
        SpringApplication.run(Bootstrap.class, args);
    }
}