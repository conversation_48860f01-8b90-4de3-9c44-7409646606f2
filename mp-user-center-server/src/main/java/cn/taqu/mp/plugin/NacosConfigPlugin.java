package cn.taqu.mp.plugin;

import cn.taqu.mp.annotation.DynamicValue;
import cn.taqu.mp.annotation.RefreshScope;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.listener.AbstractSharedListener;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.client.config.NacosConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.context.properties.bind.PropertySourcesPlaceholdersResolver;
import org.springframework.boot.env.PropertySourceLoader;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.lang.NonNull;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * nacos配置中心整合
 * <p>目前nacos对于3.4.1版本没有兼容版本，所以自己实现配置监听</p>
 * <AUTHOR>
 * @date 2025/4/23 下午2:07
 */
@Slf4j
public class NacosConfigPlugin implements BeanPostProcessor, ApplicationListener<ContextRefreshedEvent> {

    private final ConfigurableEnvironment                             env;

    private final PropertySourcesPlaceholdersResolver            resolver;

    private final String                                           dataId;

    private final Map<Object, List<Field>>                  dynamicFields;

    private final Map<Object, List<Method>>                dynamicMethods;

    private final PropertySourceLoader               propertySourceLoader;

    public NacosConfigPlugin(ConfigurableApplicationContext applicationContext) {
        this.env = applicationContext.getEnvironment();
        this.dynamicFields = new HashMap<>(8);
        this.dynamicMethods = new HashMap<>(8);
        this.resolver = new PropertySourcesPlaceholdersResolver(env);
        this.propertySourceLoader = new YamlPropertySourceLoader();
        String appName = env.getProperty("spring.application.name");
        this.dataId = appName + ".yml";
        init();
    }

    private void init() {
        String configVal = watchConfig();
        if (StringUtils.isNotBlank(configVal)) merge(configVal);
    }

    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {
        refreshBeans();
    }

    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        if (AnnotationUtils.findAnnotation(bean.getClass(), RefreshScope.class) != null) {
            dynamicFields.put(bean, FieldUtils.getFieldsListWithAnnotation(bean.getClass(), DynamicValue.class));
            dynamicMethods.put(bean, MethodUtils.getMethodsListWithAnnotation(bean.getClass(), DynamicValue.class, false, true));
        }
        return bean;
    }

    private String watchConfig() {
        String address = env.getProperty("nacos.address");
        String ns = env.getProperty("nacos.namespace");
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.SERVER_ADDR, address);
        properties.put(PropertyKeyConst.NAMESPACE, ns);
        try {
            NacosConfigService configService = new NacosConfigService(properties);
            return configService.getConfigAndSignListener(dataId, "TQ_MP_GROUP", 3000, new AbstractSharedListener() {

                @Override
                public void innerReceive(String dataId, String group, String configInfo) {
                    merge(configInfo);
                    refreshBeans();
                }
            });
        } catch (NacosException e) {
            log.error("Unable to watch config", e);
            return null;
        }
    }

    private void merge(String yaml) {
        try {
            List<PropertySource<?>> newSources = propertySourceLoader.load(
                dataId,
                new ByteArrayResource(yaml.getBytes())
            );
            MutablePropertySources sources = env.getPropertySources();
            if (sources.contains(dataId)) {
                if (newSources.isEmpty()) {
                    sources.remove(dataId);
                } else {
                    sources.replace(dataId, newSources.getFirst());
                }
            } else if (!newSources.isEmpty()) {
                sources.addFirst(newSources.getFirst());
            }
        } catch (IOException e) {
            log.error("Unable to merge config", e);
        }
    }

    private void refreshBeans() {
        dynamicFields.forEach((bean, fields) -> {
            for (var f : fields) {
                DynamicValue ann = f.getAnnotation(DynamicValue.class);
                f.setAccessible(true);
                try {
                    String val = (String) resolver.resolvePlaceholders(ann.value());
                    Object v = val;
                    if (Boolean.class.isAssignableFrom(f.getType()) || boolean.class.isAssignableFrom(f.getType())) {
                        v = Boolean.parseBoolean(val);
                    } else if (Integer.class.isAssignableFrom(f.getType()) || int.class.isAssignableFrom(f.getType())) {
                        v = Integer.parseInt(val);
                    } else if (Float.class.isAssignableFrom(f.getType()) || float.class.isAssignableFrom(f.getType())) {
                        v = Float.parseFloat(val);
                    } else if (Double.class.isAssignableFrom(f.getType()) || double.class.isAssignableFrom(f.getType())) {
                        v = Double.parseDouble(val);
                    }
                    f.set(bean, v);
                    log.info("field {} set to {}", f, v);
                } catch (Exception e) {
                    log.warn("Unable to set dynamic value for {} ", f);
                }
            }
        });
        dynamicMethods.forEach((bean, ms) -> {
            for (var m : ms) {
                DynamicValue ann = m.getAnnotation(DynamicValue.class);
                var p = m.getParameters()[0];
                try {
                    String val = (String) resolver.resolvePlaceholders(ann.value());
                    Object v = val;
                    if (Boolean.class.isAssignableFrom(p.getType()) || boolean.class.isAssignableFrom(p.getType())) {
                        v = Boolean.parseBoolean(val);
                    } else if (Integer.class.isAssignableFrom(p.getType()) || int.class.isAssignableFrom(p.getType())) {
                        v = Integer.parseInt(val);
                    } else if (Float.class.isAssignableFrom(p.getType()) || float.class.isAssignableFrom(p.getType())) {
                        v = Float.parseFloat(val);
                    } else if (Double.class.isAssignableFrom(p.getType()) || double.class.isAssignableFrom(p.getType())) {
                        v = Double.parseDouble(val);
                    }
                    m.invoke(bean, v);
                    log.info("method {} set with {}", m, v);
                } catch (Exception e) {
                    log.warn("Unable to set dynamic value via setter ", e);
                }
            }
        });
    }
}
