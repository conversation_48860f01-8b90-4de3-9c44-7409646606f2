[{"interfaces": ["cn.taqu.mp.dao.MemberDao", "org.springframework.data.repository.Repository", "org.springframework.transaction.interceptor.TransactionalProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["io.lettuce.core.api.sync.RedisCommands", "io.lettuce.core.cluster.api.sync.RedisClusterCommands"]}, {"interfaces": ["jakarta.persistence.EntityManagerFactory", "org.springframework.orm.jpa.EntityManagerFactoryInfo"]}, {"interfaces": ["jakarta.servlet.http.HttpServletRequest"]}, {"interfaces": ["jakarta.servlet.http.HttpSession"]}, {"interfaces": ["java.lang.reflect.GenericArrayType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.ParameterizedType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.TypeVariable", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.WildcardType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.sql.Connection"]}, {"interfaces": ["net.bytebuddy.description.method.MethodDescription$InDefinedShape$AbstractBase$Executable"]}, {"interfaces": ["net.bytebuddy.description.method.ParameterDescription$ForLoadedParameter$Parameter"]}, {"interfaces": ["net.bytebuddy.description.method.ParameterList$ForLoadedExecutable$Executable"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDefinition$Sort$AnnotatedType"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$ForLoadedType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$Delegator$ForLoadedExecutableExceptionType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$Delegator$ForLoadedExecutableParameterType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$Delegator$ForLoadedMethodReturnType$Dispatcher"]}, {"interfaces": ["net.bytebuddy.description.type.TypeDescription$Generic$AnnotationReader$ForComponentType$AnnotatedParameterizedType"]}, {"interfaces": ["net.bytebuddy.dynamic.loading.ClassInjector$UsingLookup$MethodHandles"]}, {"interfaces": ["net.bytebuddy.dynamic.loading.ClassInjector$UsingLookup$MethodHandles$Lookup"]}, {"interfaces": ["org.hibernate.Session", "org.springframework.orm.jpa.EntityManagerProxy"]}, {"interfaces": ["org.springframework.boot.actuate.endpoint.annotation.Endpoint"]}, {"interfaces": ["org.springframework.boot.actuate.endpoint.annotation.EndpointExtension"]}, {"interfaces": ["org.springframework.boot.context.properties.ConfigurationProperties"]}, {"interfaces": ["org.springframework.data.jpa.repository.Query"]}, {"interfaces": ["org.springframework.data.jpa.repository.support.CrudMethodMetadata", "org.springframework.aop.SpringProxy", "org.springframework.aop.framework.Advised", "org.springframework.core.DecoratingProxy"]}, {"interfaces": ["org.springframework.data.redis.connection.StringRedisConnection", "org.springframework.data.redis.connection.DecoratedRedisConnection"]}, {"interfaces": ["org.springframework.jdbc.datasource.ConnectionProxy"]}, {"interfaces": ["org.springframework.web.bind.annotation.ControllerAdvice"]}, {"interfaces": ["org.springframework.web.bind.annotation.ExceptionHandler"]}, {"interfaces": ["org.springframework.web.bind.annotation.RequestMapping"]}]