[{"name": "[Lcom.sun.management.internal.DiagnosticCommandArgumentInfo;"}, {"name": "[Lcom.sun.management.internal.DiagnosticCommandInfo;"}, {"name": "com.sun.management.internal.DiagnosticCommandArgumentInfo", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "boolean", "boolean", "int"]}]}, {"name": "com.sun.management.internal.DiagnosticCommandInfo", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.util.List"]}]}, {"name": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.String", "methods": [{"name": "lastIndexOf", "parameterTypes": ["int"]}, {"name": "substring", "parameterTypes": ["int"]}]}, {"name": "java.lang.System", "methods": [{"name": "getProperty", "parameterTypes": ["java.lang.String"]}, {"name": "setProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.util.Arrays", "methods": [{"name": "asList", "parameterTypes": ["java.lang.Object[]"]}]}, {"name": "org.springframework.boot.loader.launch.JarLauncher", "methods": [{"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "sun.management.VMManagementImpl", "fields": [{"name": "compTimeMonitoringSupport"}, {"name": "currentThreadCpuTimeSupport"}, {"name": "objectMonitorUsageSupport"}, {"name": "otherThreadCpuTimeSupport"}, {"name": "remoteDiagnosticCommandsSupport"}, {"name": "synchronizerUsageSupport"}, {"name": "threadAllocatedMemorySupport"}, {"name": "threadContentionMonitoringSupport"}]}]