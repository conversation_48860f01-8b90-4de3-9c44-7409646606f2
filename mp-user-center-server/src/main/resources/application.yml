config-center:
  biz:
    name: mp
  application:
    name: mp-user-center

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  metrics:
    tags:
      application: mp-user-center
  info:
    git:
      mode: full
  health:
    defaults:
      enabled: false

spring:
  backend:
    prefix: /backend/v1
  threads:
    virtual:
      enabled: true
  profiles:
    active: dev
  application:
    name: mp-user-center
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    # 是否开启jackson对null值的默认处理
    null-default: false
  jpa:
    database: MYSQL
    show-sql: true



server:
  port: 8888
  tomcat:
    mbeanregistry:
      enabled: true
  servlet:
    context-path: /mp-user-center

service:
  index: ${config-center.application.name}
  code: ${config-center.application.name}
  name: ${config-center.application.name}

datasource:
  driver-class-name: com.mysql.jdbc.Driver
  hikari:
    maximum-pool-size: 50
    minimum-idle: 10
    auto-commit: true
    idle-timeout: 30000
    max-lifetime: 180000
    connection-timeout: 30000
    connection-test-query: SELECT 1
    init-sql: set names utf8mb4
    ds:
      pool-name: DataPoolHikari

nacos:
  address: @nacos.addr@
  namespace: @nacos.namespace@

warmup:
  client:
    maxAllowedWarmupSeconds: 200
    healthReserveSeconds: 15