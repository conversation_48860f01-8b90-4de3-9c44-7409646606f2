[{"path": "/api?service=info&method=getInfoByUuid", "contentType": "application/x-www-form-urlencoded", "total": 15000, "initRate": 10, "rate": 100, "payload": "form=W1siYmdqYmJkZWVkZWNhZmZjYiIsImJnamZiYWJoamhjZ2djZmoiLCJiZ2piYmRlaGNpamFiamRhIl0sWyJ1dWlkIiwiYWNjb3VudF9uYW1lIiwic2V4X3R5cGUiLCJhdmF0YXIiLCJhZ2UiLCJsb2dpbl9hcHBjb2RlIiwibG9naW5fY2xvbmVkIiwiY3JlYXRlX3RpbWUiLCJwbGF0Zm9ybV9pZCIsImFjY291bnRfc3RhdHVzIl1d"}, {"path": "/api?service=info&method=getFullDetailInfo", "contentType": "application/x-www-form-urlencoded", "total": 1600, "initRate": 8, "rate": 30, "payload": "form=W1siZGdlMzZmMHA2bWJzIiwiZGdnMGlub2ozdDRzIiwiYmdpZGZkaGppYWlhaGlpaSJdLFsidXVpZCIsImFjY291bnRfbmFtZSIsInNleF90eXBlIiwiYXZhdGFyIiwiYWdlIiwibG9naW5fYXBwY29kZSIsImxvZ2luX2Nsb25lZCIsImNyZWF0ZV90aW1lIiwicGxhdGZvcm1faWQiLCJhY2NvdW50X3N0YXR1cyIsInJlYWxfYXZhdGFyX2NlcnRpZmljYXRpb24iLCJpc19iaW5kX21vYmlsZSIsInZvaWNlX2NlcnRpZmljYXRpb24iLCJpcF9jaXR5IiwiYmFzZWFkZHJfY2l0eSJdXQ=="}, {"path": "/api?service=device&method=getTokenByUuid", "contentType": "application/x-www-form-urlencoded", "total": 1600, "initRate": 2, "rate": 15, "payload": "form=WyJiZ2piYmRlZWRlY2FmZmNiIiwiYmdqZmJhYmhqaGNnZ2NmaiJd"}]