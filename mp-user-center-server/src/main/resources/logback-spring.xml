<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="log.dir" value="./data/logs/sjava"/>
    <property name="systemCode" value="mp-user-center"/>
    <property name="CONSOLE_LOG_PATTERN"
              value="%yellow(%date{yyyy-MM-dd HH:mm:ss.SSS}) %boldYellow([%thread]) %highlight(%-5level) %boldGreen(%logger{50}) - %msg%n"/>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
<!--        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">-->
<!--            <layout class="cn.taqu.core.log.LogbackLayout"/>-->
<!--        </encoder>-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>

    </appender>

    <!-- trace日志 -->
    <appender name="traceFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 增加系统编号区分，避免多个容器使用同个日志文件时误删 -->
            <fileNamePattern>${log.dir}/${systemCode}-trace.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!--按天滚动-->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <!--单个日志文件最大，也会滚动-->
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 必须设置保存时间，否则totalSizeCap不生效-->
            <maxHistory>1</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.soa.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>TRACE</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- debug日志 -->
    <appender name="debugFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 增加系统编号区分，避免多个容器使用同个日志文件时误删 -->
            <fileNamePattern>${log.dir}/${systemCode}-debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <!--单个日志文件最大，也会滚动-->
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 必须设置保存时间，否则totalSizeCap不生效-->
            <maxHistory>1</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.soa.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- info日志 -->
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 增加系统编号区分，避免多个容器使用同个日志文件时误删 -->
            <fileNamePattern>${log.dir}/${systemCode}-info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <!--单个日志文件最大，也会滚动-->
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 必须设置保存时间，否则totalSizeCap不生效-->
            <maxHistory>5</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.soa.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- warn日志 -->
    <appender name="warnFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 增加系统编号区分，避免多个容器使用同个日志文件时误删 -->
            <fileNamePattern>${log.dir}/${systemCode}-warn.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <!--单个日志文件最大，也会滚动-->
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 必须设置保存时间，否则totalSizeCap不生效-->
            <maxHistory>5</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.soa.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- error日志 -->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 增加系统编号区分，避免多个容器使用同个日志文件时误删 -->
            <fileNamePattern>${log.dir}/${systemCode}-error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <!--单个日志文件最大，也会滚动-->
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- 必须设置保存时间，否则totalSizeCap不生效-->
            <maxHistory>5</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.soa.log.LogbackLayout"/>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="accessLogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./data/logs/nginx/${systemCode}-access.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFileNamingAndTriggeringPolicy">
                <maxFileSize>200MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>1</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.taqu.soa.log.LogMsgLayout"/>
        </encoder>
    </appender>

    <logger name="access_log" level="INFO" additivity="false">
        <appender-ref ref="accessLogFile"/>
    </logger>

    <logger name="cn.taqu" level="INFO"/>
    <logger name="jdbc.sqltiming" level="ERROR"/>
    <logger name="jdbc.sqlonly" level="WARN"/>
    <logger name="jdbc.resultset" level="ERROR"/>
    <logger name="jdbc.audit" level="ERROR"/>
    <logger name="jdbc.connection" level="ERROR"/>
    <logger name="org.elasticsearch" level="INFO"/>
    <logger name="org.springframework.kafka" level="INFO"/>
    <logger name="org.springframework.boot" level="ERROR"/>
    <logger name="org.apache.rocketmq" level="ERROR"/>

    <springProfile name="prod">
        <logger name="cn.taqu" level="INFO"/>
        <root level="INFO">
            <appender-ref ref="errorFile"/>
            <appender-ref ref="warnFile"/>
            <appender-ref ref="infoFile"/>
        </root>
    </springProfile>

    <springProfile name="local,dev">
        <logger name="cn.taqu" level="Info"/>
        <logger name="cn.taqu.mp.audit.repository.dao" level="debug" additivity="false">
            <appender-ref ref="console"/>
        </logger>
        <root level="Info">
            <appender-ref ref="errorFile"/>
            <appender-ref ref="warnFile"/>
            <appender-ref ref="infoFile"/>
            <appender-ref ref="debugFile"/>
            <appender-ref ref="traceFile"/>
            <appender-ref ref="console"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="info">
            <appender-ref ref="errorFile"/>
            <appender-ref ref="warnFile"/>
            <appender-ref ref="infoFile"/>
            <appender-ref ref="console"/>
        </root>
    </springProfile>

</configuration>
