<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.taqu</groupId>
        <artifactId>tq-mp-common</artifactId>
        <version>3.1.4</version>
    </parent>

    <groupId>cn.taqu.mp</groupId>
    <artifactId>mp-user-center-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>mp-user-center-common</module>
        <module>mp-user-center-server</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <pulsar.client.version>3.0.6</pulsar.client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.taqu.mp</groupId>
                <artifactId>mp-user-center-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pulsar</groupId>
                <artifactId>pulsar-client</artifactId>
                <version>${pulsar.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pulsar</groupId>
                <artifactId>pulsar-client-api</artifactId>
                <version>${pulsar.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pulsar</groupId>
                <artifactId>pulsar-client-admin-api</artifactId>
                <version>${pulsar.client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>native</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <extensions>true</extensions>
                        <executions>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <fallback>false</fallback>
                            <mainClass>cn.taqu.mp.Bootstrap</mainClass>
                            <buildArgs>
                                <buildArg>--verbose</buildArg>
                            </buildArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile> 			<!--test环境-->
            <id>dev</id>
            <properties>
                <app.active>dev</app.active>
                <nacos.namespace>1d5f68ed-1204-4b50-a7a4-705d5f335e76</nacos.namespace>
                <nacos.addr>mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848</nacos.addr>
            </properties>
        </profile>

        <profile> 			<!--test环境-->
            <id>test</id>
            <properties>
                <app.active>test</app.active>
                <nacos.namespace>780f0917-8c08-4908-820c-f1d023c180c2</nacos.namespace>
                <nacos.addr>mse-49d4af94-nacos-ans.mse.aliyuncs.com:8848</nacos.addr>
            </properties>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <app.active>prod</app.active>
                <nacos.namespace>369b6d46-1e31-44b6-8a7a-8d9e6633d9b1</nacos.namespace>
                <nacos.addr>mse-837d4fa4-nacos-ans.mse.aliyuncs.com:8848</nacos.addr>
            </properties>
        </profile>
    </profiles>
</project>